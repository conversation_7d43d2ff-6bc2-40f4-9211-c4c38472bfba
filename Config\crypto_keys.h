#ifndef CRYPTO_KEYS_H
#define CRYPTO_KEYS_H
#include <string>
#include "vmp/VMProtect.h"

//// RSA 公钥
//const std::string CLIENT_PAK_RSA_PUB_KEY_PEM = R"(-----B<PERSON>IN PUBLIC KEY-----
//MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlSCIznTEBRgZ48E/B+s6
//XeOMrlsqoAHrFn+1j/KSyIvn9izw3hwsSSOijRklBf0lncNBrPW1oXXco1gAep9w
//p2ScuiziHo2OtVq0iBs+KoprEV6u7lRhcbJdIxjIj5FVXzN6V/3ldjCSJjnnwBL7
//vUNRjnsY7JAXDuuPYD2+ejEzvZJ5qw/msVb9LuQiEHzBIGijP/PCVwwup7g+0Or4
//pnC6E3MGkeVaLd5rcNhpJ0zicUuwxkXprzv1E6R0OTVEmgyuA27mIUcZAAQXmKbb
//41riXoQ2v+spyoj/2cH8PIHYBiQfJj2CAt/3qT3H2nddlySIluhjVEAZq5UInWBK
//OQIDAQAB
//-----END PUBLIC KEY-----)";

// 客户端 RSA 私钥(用作数据包签名)
const std::string CLIENT_PAK_RSA_PRI_KEY_PEM = VSA(R"(***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)");

// 服务器 RSA 公钥(用作数据包验证签名)
const std::string SERVER_PAK_RSA_PUB_KEY_PEM = VSA(R"(-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhiXBTpNEKrxVhu+riNsi
Fu9OANvmuEFk9NT4DEU4IpqqZYqoOE9eYo+5Ewt8UdqPDCYEGSkpbvrwsms/9eU2
i63QvMpftGqQiTS3X1QQiMwI7H2NNzzu7qM/QtfAFx5unm2/iOhjXgdl3kM/C7wZ
jVkO98g4Xkmtcke8dAziS7EyrMxcqYoIFMvCz8MjkS8YY2NA06e5ANvwS5yhtkhL
hLvSUYs/+4hFO+FB/Hk8n66yAYkEs/XxTM85fxMGQAB3gBn1fqmbIYsTQXumKdxx
IY0P8GwuOgCEe7GztHN2vnTVyre7SdG2yAWVUlMP0t2t/WaF7qmu+d/ogsRe/T8y
JwIDAQAB
-----END PUBLIC KEY-----)");

const std::string AES_KEY = VSA(R"(********-6FA4-4B08-90A6-017D7A0D2E6F)");
#endif // CRYPTO_KEYS_H
