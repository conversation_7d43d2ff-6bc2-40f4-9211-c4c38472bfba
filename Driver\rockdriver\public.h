#ifndef _PUBLIC_
#define _PUBLIC_
#include "../../lib/Veil/Veil.h"
#include "custom_define.h"
#include "kernel_api.h"
#include "oxorany.h"
#include <optional>
#include <stdexcept>
#include <type_traits>

#define ImpCall(a,...) (a(__VA_ARGS__))

#define DynamicFuncCall(moduleName, funcName, ...)                                      \
    [&]() -> std::optional<decltype(funcName(__VA_ARGS__))>                             \
    {                                                                                   \
        std::string modulePath = moduleName;                                            \
        if (modulePath == std::string(oxorany("ntoskrnl.exe")))                         \
        {                                                                               \
            modulePath = oxorany("\\SystemRoot\\system32\\ntoskrnl.exe");               \
        }                                                                               \
        auto func = reinterpret_cast<decltype(&funcName)>(                              \
            utils::KGetProcAddress(modulePath, oxorany(#funcName)));                    \
        if (func == nullptr)                                                            \
        {                                                                               \
            return std::nullopt;                                                        \
        }                                                                               \
        return func(__VA_ARGS__);                                                       \
    }()

#define DynamicFuncCallA(funcName, ...)                                                 \
    [&]() -> std::optional<decltype(funcName(__VA_ARGS__))>                             \
    {                                                                                   \
        auto func = reinterpret_cast<decltype(&funcName)>(                              \
            utils::KGetProcAddress(oxorany(#funcName)));                                \
        if (func == nullptr)                                                            \
        {                                                                               \
            return std::nullopt;                                                        \
        }                                                                               \
        return func(__VA_ARGS__);                                                       \
    }()

#define DynamicFuncCallVoid(funcName, ...)                                              \
    [&]() -> decltype(auto)                                                             \
    {                                                                                   \
        auto func = reinterpret_cast<decltype(&funcName)>(                              \
            utils::KGetProcAddress(oxorany(#funcName)));                                \
        if (!func)                                                                      \
        {                                                                               \
            throw std::runtime_error("Failed!");                                        \
        }                                                                               \
        return func(__VA_ARGS__);                                                       \
    }()

#endif
