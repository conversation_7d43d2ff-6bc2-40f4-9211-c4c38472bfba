#pragma once
#include "public.h"
#include <string>
namespace process
{
	PEPROCESS Lookup(HANDLE process_id);

	void Unlookup(PEPROCESS eprocess);

	char* ShortName(PEPROCESS eprocess);

	bool GetFullPath(HANDLE processHandle, std::wstring& processPath);

	bool IsCsrss(PEPROCESS eprocess);

	bool IsLsass(PEPROCESS eprocess);

	bool Is64(PEPROCESS eprocess);

	uint32_t ParentId(PEPROCESS eprocess);

	bool IsParent(PEPROCESS eprocess, PEPROCESS parent);

	PVOID HandleTable(PEPROCESS eprocess);

	PVOID ReferenceHandleTable(PEPROCESS eprocess);

	void DereferenceHandleTable(PEPROCESS eprocess);

	bool EnumHandle(uint32_t process_id, PENUM_HANDLETABLE_ROUTINE func, PVOID context = nullptr);

	bool OpenProcess(HANDLE ProcessId, HANDLE& ProcessHandle);
}