#pragma once
#include <string>
#include <set>
#include <map>
#include <array>
#include <vector>
#include <filesystem>
#include <cstdio>
#include <iostream>
#include <fstream>
#include <unordered_map>
#include <memory>
#include <mutex>
#include <queue>
#include <sstream>

#include <Config.h>
#include <vmp/VMProtect.h>
#include <utils/utils.h>
#include "oxorany.h"

using nlohmann::json;

#ifdef _DEBUG
#define LOG(format,...) RGContext::instance()->logger.Log(__FUNCTION__, format, __VA_ARGS__)
#else
#define LOG(format, ...)
#endif
