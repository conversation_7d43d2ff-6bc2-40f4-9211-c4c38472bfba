#include "win_thread.h"
#include <Windows.h>

uint32_t win_thread::GetSelfThreadId()
{
    return GetCurrentThreadId();
}

win_thread::~win_thread()
{
    Terminate();
}

uint32_t win_thread::GetThreadId()
{
	return _threadId;
}

bool win_thread::Pause()
{
    if (_threadId == 0 || _paused) return false;

    HANDLE hThread = OpenThread(THREAD_SUSPEND_RESUME, FALSE, _threadId);
    if (hThread) {
        DWORD ret = ::SuspendThread(hThread);
        CloseHandle(hThread);

        if (ret != (DWORD)-1) {
            _paused = true;
            return true;
        }
    }
    return false;
}

bool win_thread::Resume()
{
    if (_threadId == 0 || !_paused) return false;

    HANDLE hThread = OpenThread(THREAD_SUSPEND_RESUME, FALSE, _threadId);
    if (hThread) {
        DWORD ret = ::ResumeThread(hThread);
        CloseHandle(hThread);

        if (ret != (DWORD)-1) {
            _paused = false;
            return true;
        }
    }
    return false;
}

#pragma warning(push)
#pragma warning(disable : 6258)
bool win_thread::Terminate()
{
    if (_threadId == 0) return false;

    if (GetCurrentThreadId() == _threadId) {
        _threadId = 0;
        ::ExitThread(0);
        return true;
    }

    HANDLE hThread = OpenThread(SYNCHRONIZE | THREAD_TERMINATE, FALSE, _threadId);
    if (!hThread) return false;

    DWORD waitResult = WaitForSingleObject(hThread, 5000);
    if (waitResult == WAIT_OBJECT_0) {
        CloseHandle(hThread);
        _threadId = 0;
        return true;
    }

    BOOL ret = ::TerminateThread(hThread, 0);
    CloseHandle(hThread);
    if (ret != 0) {
        _threadId = 0;
        return true;
    }
    return false;
}
#pragma warning(pop)
