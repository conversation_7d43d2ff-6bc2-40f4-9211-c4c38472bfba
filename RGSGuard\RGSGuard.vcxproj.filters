﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Config">
      <UniqueIdentifier>{0ca9d0a1-7506-4c6e-a90e-47ea25569e15}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util">
      <UniqueIdentifier>{77bcd9c5-a074-4fd6-b25c-6b52b3b37fa9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Util\Timer">
      <UniqueIdentifier>{ab7b7e43-55ed-4bd3-8eaa-eb2be7b7ed48}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard">
      <UniqueIdentifier>{c306d0a1-6b5d-48dd-94cc-3eab5a2e6fdf}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\RGSMod">
      <UniqueIdentifier>{d7da822e-3178-4bf5-97c4-01c2e93212b5}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\RGSMod\Machine">
      <UniqueIdentifier>{cb1e1b4f-5340-40be-859a-b2d4f56ec242}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\RGContext">
      <UniqueIdentifier>{22f85631-005d-46af-8be9-0b410259938b}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\DataCenter">
      <UniqueIdentifier>{b566c055-dfb5-46a0-837d-35a9daaa619e}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\ProtoFactory">
      <UniqueIdentifier>{04ec4ccd-22fc-4c67-9a68-3534eada427a}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\ProtectData">
      <UniqueIdentifier>{02619590-46f8-4585-b274-f0a20b1f1cd4}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\RGDriver">
      <UniqueIdentifier>{b56cb89d-6287-468f-9679-1c85c2a1eed0}</UniqueIdentifier>
    </Filter>
    <Filter Include="RGSGuard\RGDriver\xor_encrypt">
      <UniqueIdentifier>{64f07298-2f60-4c69-8165-3e58f6e1394b}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Main.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="RGDriver.cpp">
      <Filter>RGSGuard\RGDriver</Filter>
    </ClCompile>
    <ClCompile Include="RGScanEnv.cpp">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClCompile>
    <ClCompile Include="RGScanGame.cpp">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClCompile>
    <ClCompile Include="RGScanProcess.cpp">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClCompile>
    <ClCompile Include="RGScreenshot.cpp">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClCompile>
    <ClCompile Include="RGProtectSelf.cpp">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClCompile>
    <ClCompile Include="RGContext.cpp">
      <Filter>RGSGuard\RGContext</Filter>
    </ClCompile>
    <ClCompile Include="RGMachineId.cpp">
      <Filter>RGSGuard\RGSMod\Machine</Filter>
    </ClCompile>
    <ClCompile Include="ProtectData.cpp">
      <Filter>RGSGuard\ProtectData</Filter>
    </ClCompile>
    <ClCompile Include="RGDataCenter.cpp">
      <Filter>RGSGuard\DataCenter</Filter>
    </ClCompile>
    <ClCompile Include="RGServiceManager.cpp">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClCompile>
    <ClCompile Include="timer_thread.cpp">
      <Filter>Util\Timer</Filter>
    </ClCompile>
    <ClCompile Include="ProtoFactory.cpp">
      <Filter>RGSGuard\ProtoFactory</Filter>
    </ClCompile>
    <ClCompile Include="..\Lib\xor_encrypt\xor_encrypt.cpp">
      <Filter>RGSGuard\RGDriver\xor_encrypt</Filter>
    </ClCompile>
    <ClCompile Include="Logger.cpp">
      <Filter>Util</Filter>
    </ClCompile>
    <ClCompile Include="CrashHandler.cpp">
      <Filter>Util</Filter>
    </ClCompile>
    <ClCompile Include="win_thread.cpp">
      <Filter>Util\Timer</Filter>
    </ClCompile>
    <ClCompile Include="oxorany.cpp">
      <Filter>Util</Filter>
    </ClCompile>
    <ClCompile Include="RGSGuard.cpp">
      <Filter>RGSGuard</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="global.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="RGSGuard.h">
      <Filter>RGSGuard</Filter>
    </ClInclude>
    <ClInclude Include="RGDriver.h">
      <Filter>RGSGuard\RGDriver</Filter>
    </ClInclude>
    <ClInclude Include="RGScanEnv.h">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClInclude>
    <ClInclude Include="RGScanGame.h">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClInclude>
    <ClInclude Include="RGScanProcess.h">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClInclude>
    <ClInclude Include="RGScreenshot.h">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClInclude>
    <ClInclude Include="RGProtectSelf.h">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClInclude>
    <ClInclude Include="RGContext.h">
      <Filter>RGSGuard\RGContext</Filter>
    </ClInclude>
    <ClInclude Include="RGMachineId.h">
      <Filter>RGSGuard\RGSMod\Machine</Filter>
    </ClInclude>
    <ClInclude Include="win_common.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="ProtectData.h">
      <Filter>RGSGuard\ProtectData</Filter>
    </ClInclude>
    <ClInclude Include="RGDataCenter.h">
      <Filter>RGSGuard\DataCenter</Filter>
    </ClInclude>
    <ClInclude Include="RGServiceManager.h">
      <Filter>RGSGuard\RGSMod</Filter>
    </ClInclude>
    <ClInclude Include="..\Config\Config.h">
      <Filter>Config</Filter>
    </ClInclude>
    <ClInclude Include="..\Config\crypto_keys.h">
      <Filter>Config</Filter>
    </ClInclude>
    <ClInclude Include="timer_mod.h">
      <Filter>Util\Timer</Filter>
    </ClInclude>
    <ClInclude Include="timer_thread.h">
      <Filter>Util\Timer</Filter>
    </ClInclude>
    <ClInclude Include="ProtoFactory.h">
      <Filter>RGSGuard\ProtoFactory</Filter>
    </ClInclude>
    <ClInclude Include="..\Config\deliver_define.h">
      <Filter>Config</Filter>
    </ClInclude>
    <ClInclude Include="..\Lib\xor_encrypt\xor_encrypt.h">
      <Filter>RGSGuard\RGDriver\xor_encrypt</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CrashHandler.h">
      <Filter>Util</Filter>
    </ClInclude>
    <ClInclude Include="Logger.h">
      <Filter>Util</Filter>
    </ClInclude>
    <ClInclude Include="win_thread.h">
      <Filter>Util\Timer</Filter>
    </ClInclude>
    <ClInclude Include="oxorany.h">
      <Filter>Util</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="RGSGuard.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Image Include="Main.ico">
      <Filter>资源文件</Filter>
    </Image>
  </ItemGroup>
</Project>