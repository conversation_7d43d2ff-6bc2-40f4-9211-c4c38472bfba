﻿<?xml version="1.0" encoding="utf-8"?>


<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">  
  <PropertyGroup Condition="'$(NuGetImportBeforeCppTargetsComplete)' != 'true'">
    <ForceImportBeforeCppTargets>$(YY_Import_Helper_Backup_ForceImportBeforeCppTargets)</ForceImportBeforeCppTargets>
    <YY_Import_Helper_Backup_ForceImportBeforeCppTargets></YY_Import_Helper_Backup_ForceImportBeforeCppTargets>
  </PropertyGroup>

  <ImportGroup Condition="'$(NuGetImportBeforeCppTargetsComplete)' != 'true'">
    <Import Project="$(NuGetImportBeforeCppTargets)" Condition="'$(NuGetImportBeforeCppTargets)' != ''"/>
  </ImportGroup>
  
  <PropertyGroup>
    <NuGetImportBeforeCppTargetsComplete>true</NuGetImportBeforeCppTargetsComplete>
  </PropertyGroup>
</Project>