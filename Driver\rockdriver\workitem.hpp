#include "pch.hpp"


class sync_work
{
public:
    typedef void(*fn_callback)(ptr context);

    sync_work() : _workitem(nullptr), _callback(nullptr), _context(nullptr), _comple()
    {

    }

    ~sync_work()
    {
        if (_workitem)
        {
            ImpCall(IoFreeWorkItem, _workitem);
        }
    }

    static void initialize(PDEVICE_OBJECT device_object)
    {
        _device_object = device_object;
    }

    void execute(fn_callback routine, ptr context)
    {
        ImpCall(KeInitializeEvent, &_comple, NotificationEvent, FALSE);
        _workitem = ImpCall(IoAllocateWorkItem, _device_object);
        if (_workitem)
        {
            _callback = routine;
            _context = context;
            ImpCall(IoQueueWorkItem, _workitem, sync_work::wrapper_routine, DelayedWorkQueue, this);
            ImpCall(KeWaitForSingleObject, &_comple, Executive, KernelMode, FALSE, nullptr);
        }
    }

    static void wrapper_routine(PDEVICE_OBJECT device_object, ptr context)
    {
        auto _workitem = static_cast<sync_work*>(context);
        _workitem->_callback(_workitem->_context);
        ImpCall(KeSetEvent, &_workitem->_comple, 0, FALSE);
    }

private:

    KEVENT _comple;
    PIO_WORKITEM _workitem;

    ptr _context;
    fn_callback _callback;

    static PDEVICE_OBJECT _device_object;
};

inline PDEVICE_OBJECT sync_work::_device_object = nullptr;