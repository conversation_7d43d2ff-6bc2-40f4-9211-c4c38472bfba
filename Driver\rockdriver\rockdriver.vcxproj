﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\ucxxrt.3.0.7\build\ucxxrt.props" Condition="Exists('..\packages\ucxxrt.3.0.7\build\ucxxrt.props')" />
  <Import Project="..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.props" Condition="Exists('..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.props')" />
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7EFA6449-1A21-4AE0-8B3F-BC8B3DAABD8A}</ProjectGuid>
    <TemplateGuid>{1bc93793-694f-48fe-9372-81e2b05556fd}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <Configuration>Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <RootNamespace>RockGuard</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
    <ProjectName>rockdriver</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Universal</DriverTargetPlatform>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Universal</DriverTargetPlatform>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Universal</DriverTargetPlatform>
    <Driver_SpectreMitigation>false</Driver_SpectreMitigation>
    <ALLOW_DATE_TIME>1</ALLOW_DATE_TIME>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverType>KMDF</DriverType>
    <DriverTargetPlatform>Universal</DriverTargetPlatform>
    <Driver_SpectreMitigation>false</Driver_SpectreMitigation>
    <ALLOW_DATE_TIME>1</ALLOW_DATE_TIME>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(VC_IncludePath);$(IncludePath);$(KMDF_INC_PATH)$(KMDF_VER_PATH)</IncludePath>
    <EnableInf2cat>false</EnableInf2cat>
    <OutDir>..\..\Bin\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>..\..\Bin\$(ProjectName)\$(Configuration)\$(Platform)\obj\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(VC_IncludePath);$(IncludePath);$(KMDF_INC_PATH)$(KMDF_VER_PATH)</IncludePath>
    <EnableInf2cat>false</EnableInf2cat>
    <OutDir>..\..\Bin\$(Configuration)\$(Platform)\</OutDir>
    <IntDir>..\..\Bin\$(ProjectName)\$(Configuration)\$(Platform)\obj\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <PreprocessorDefinitions>RockGuard_Message;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories);..\..\Lib</AdditionalIncludeDirectories>
    </ClCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Link>
      <EntryPointSymbol>DriverEntry</EntryPointSymbol>
      <AdditionalDependencies>VMProtectDDK64.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\lib\Veil\Library\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>RockGuard_Message;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DebugInformationFormat>None</DebugInformationFormat>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories);..\..\Lib</AdditionalIncludeDirectories>
    </ClCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>VMProtectDDK64.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\..\lib\Veil\Library\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <AssemblyDebug>false</AssemblyDebug>
      <AdditionalOptions>
      </AdditionalOptions>
      <StripPrivateSymbols>
      </StripPrivateSymbols>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>RockGuard_Message;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories);..\..\Lib</AdditionalIncludeDirectories>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>RockGuard_Message;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories);..\..\Lib</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\Lib\xor_encrypt\xor_encrypt.cpp" />
    <ClCompile Include="lower_handleaccess.cpp" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="notify_callback.cpp" />
    <ClCompile Include="oxorany.cpp" />
    <ClCompile Include="process.cpp" />
    <ClCompile Include="protect_rockac.cpp" />
    <ClCompile Include="rockguard.cpp" />
    <ClCompile Include="rock_context.cpp" />
    <ClCompile Include="rock_driver.cpp" />
    <ClCompile Include="scheduler.cpp" />
    <ClCompile Include="selfunload.cpp" />
    <ClCompile Include="syncwork.cpp" />
    <ClCompile Include="timed_heartbeat.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Lib\xor_encrypt\xor_encrypt.h" />
    <ClInclude Include="base_type.hpp" />
    <ClInclude Include="ctx.hpp" />
    <ClInclude Include="custom_define.h" />
    <ClInclude Include="defines.hpp" />
    <ClInclude Include="dynamic.hpp" />
    <ClInclude Include="file.hpp" />
    <ClInclude Include="handle.hpp" />
    <ClInclude Include="kthread.hpp" />
    <ClInclude Include="lower_handleaccess.h" />
    <ClInclude Include="notify_callback.h" />
    <ClInclude Include="oxorany.h" />
    <ClInclude Include="oxorany_include.h" />
    <ClInclude Include="pch.hpp" />
    <ClInclude Include="kernel_api.h" />
    <ClInclude Include="log.hpp" />
    <ClInclude Include="main.hpp" />
    <ClInclude Include="notification.hpp" />
    <ClInclude Include="pe.hpp" />
    <ClInclude Include="process.h" />
    <ClInclude Include="process.hpp" />
    <ClInclude Include="protect_rockac.h" />
    <ClInclude Include="public.h" />
    <ClInclude Include="registry.hpp" />
    <ClInclude Include="rockguard.h" />
    <ClInclude Include="rock_context.h" />
    <ClInclude Include="rock_driver.h" />
    <ClInclude Include="scheduler.h" />
    <ClInclude Include="selfunload.h" />
    <ClInclude Include="string.hpp" />
    <ClInclude Include="syncwork.h" />
    <ClInclude Include="thread.hpp" />
    <ClInclude Include="timed_heartbeat.h" />
    <ClInclude Include="timer_mod.h" />
    <ClInclude Include="timer_thread.h" />
    <ClInclude Include="utils.h" />
    <ClInclude Include="vmp.h" />
    <ClInclude Include="workitem.hpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.targets" Condition="Exists('..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.targets')" />
  </ImportGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.props'))" />
    <Error Condition="!Exists('..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\YY.NuGet.Import.Helper.1.0.0.4\build\native\YY.NuGet.Import.Helper.targets'))" />
    <Error Condition="!Exists('..\packages\ucxxrt.3.0.7\build\ucxxrt.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\ucxxrt.3.0.7\build\ucxxrt.props'))" />
  </Target>
</Project>