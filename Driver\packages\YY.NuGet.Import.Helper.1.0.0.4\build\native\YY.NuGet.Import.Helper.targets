﻿<?xml version="1.0" encoding="utf-8"?>

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" >
  <Target Name="EnsureNuGetImportsOK" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <NuGetImportBeforeCppPropsHelpText>NuGetImportBeforeCppProps 存在未执行导入动作，请考虑备选方案在紧挨着 “&lt;Import Project="%24(VCTargetsPath)\Microsoft.Cpp.props" />” 之前插入：“&lt;Import Condition="Exists('%24(NuGetForceImportBeforeCppPropsFallback)')" Project="%24(NuGetForceImportBeforeCppPropsFallback)" />”。</NuGetImportBeforeCppPropsHelpText>
      <NuGetImportAfterCppPropsHelpText>NuGetImportAfterCppProps 存在未执行导入动作，请考虑备选方案在紧挨着 “&lt;Import Project="%24(VCTargetsPath)\Microsoft.Cpp.props" />” 之后插入：“&lt;Import Condition="Exists('%24(NuGetForceImportAfterCppPropsFallback)')" Project="%24(NuGetForceImportAfterCppPropsFallback)" />”。</NuGetImportAfterCppPropsHelpText>
    </PropertyGroup>
    <Error Condition="'$(NuGetImportBeforeCppPropsComplete)' != 'true' and '$(NuGetImportBeforeCppProps)' != ''" Text="$(NuGetImportBeforeCppPropsHelpText)" />
    <Error Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true' and '$(NuGetImportAfterCppProps)' != ''" Text="$(NuGetImportAfterCppPropsHelpText)" />
  </Target>

  <!--处理NuGet PackageReference引用的Content-->
  <Target Name="YY_ResolveNuGetPackagesPackageReferenceContent" AfterTargets="BeforeClCompile" Outputs="'%(Content.FileName)'">
    <PropertyGroup>
      <ThisNuGetPackageId>%(Content.FileName)</ThisNuGetPackageId>
      <ThisNuGetPackageId Condition="'%(Content.FileName)' != '%(Content.NuGetPackageId)'"></ThisNuGetPackageId>
      <ThisNuGetPackageVersion>%(Content.NuGetPackageVersion)</ThisNuGetPackageVersion>
    </PropertyGroup>
    
    <ItemGroup Condition="'$(ThisNuGetPackageId)' != ''">
      <NuGetExConnentFiles Update="@(NuGetExConnentFiles)" Condition="'%(NuGetExConnentFiles.ExcludedFromBuild)' == '' and '%(NuGetExConnentFiles.NuGetPackageId)' == '$(ThisNuGetPackageId)'">
        <ExcludedFromBuild>false</ExcludedFromBuild>
        <NuGetPackageVersion Condition="'$(ThisNuGetPackageVersion)' != ''">$(ThisNuGetPackageVersion)</NuGetPackageVersion>
      </NuGetExConnentFiles>
    </ItemGroup>
  </Target>

  <!--处理传统NuGet引用的Content-->
  <!--2008工具集添加Txt后会变成None节点，因此我们再额外处理下None-->
  <Target Name="YY_ResolveNuGetPackagesNoneContent" AfterTargets="BeforeClCompile" Outputs="'%(None.FileName)'" >
    <PropertyGroup>
      <ThisNuGetPackageId>%(None.FileName)</ThisNuGetPackageId>
      <ThisNuGetPackageExcludedFromBuild>%(None.ExcludedFromBuild)</ThisNuGetPackageExcludedFromBuild>
    </PropertyGroup>

    <ItemGroup Condition="'$(ThisNuGetPackageExcludedFromBuild)' != 'true'">
      <NuGetExConnentFiles Update="@(NuGetExConnentFiles)" Condition="'%(NuGetExConnentFiles.ExcludedFromBuild)' == '' and '%(NuGetExConnentFiles.NuGetPackageId)' == '$(ThisNuGetPackageId)'">
        <ExcludedFromBuild>false</ExcludedFromBuild>
      </NuGetExConnentFiles>
    </ItemGroup>
  </Target>
  <!--2012开始 IDE 支持Text了，我们解析下 Text-->
  <Target Name="YY_ResolveNuGetPackagesTextContent" AfterTargets="BeforeClCompile" Outputs="'%(Text.FileName)'" >
    <PropertyGroup>
      <ThisNuGetPackageId>%(Text.FileName)</ThisNuGetPackageId>
      <ThisNuGetPackageExcludedFromBuild>%(Text.ExcludedFromBuild)</ThisNuGetPackageExcludedFromBuild>
    </PropertyGroup>

    <ItemGroup Condition="'$(ThisNuGetPackageExcludedFromBuild)' != 'true'">
      <NuGetExConnentFiles Update="@(NuGetExConnentFiles)" Condition="'%(NuGetExConnentFiles.ExcludedFromBuild)' == '' and '%(NuGetExConnentFiles.NuGetPackageId)' == '$(ThisNuGetPackageId)'">
        <ExcludedFromBuild>false</ExcludedFromBuild>
      </NuGetExConnentFiles>
    </ItemGroup>
  </Target>

  <!--处理将项插入内存中-->
  <Target Name="YY_ResolveNuGetPackages" AfterTargets="BeforeClCompile">
    <CreateItem
        Condition="'%(NuGetExConnentFiles.ExcludedFromBuild)' == 'false'"
        Include="@(NuGetExConnentFiles)">
      <Output TaskParameter="Include" ItemName="%(NuGetExConnentFiles.BuildAction)"/>
    </CreateItem>
  </Target>
</Project>