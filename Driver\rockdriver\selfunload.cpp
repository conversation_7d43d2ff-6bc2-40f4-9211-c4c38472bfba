#include "selfunload.h"
#include "utils.h"
#include "public.h"

PUNICODE_STRING g_pRegistryPath = NULL;
PWORK_QUEUE_ITEM g_pUnloadWorkItem = NULL;

#pragma warning(push)
#pragma warning(disable : 4996)

VOID SelfUnloadInit(_In_ PUNICODE_STRING registry_path) {
	g_pRegistryPath = (PUNICODE_STRING)ExAllocatePool(PagedPool, sizeof(UNICODE_STRING));
	if (g_pRegistryPath) {
		g_pRegistryPath->Buffer = (PWCH)ExAllocatePool(PagedPool, registry_path->MaximumLength);
		g_pRegistryPath->Length = registry_path->Length;
		g_pRegistryPath->MaximumLength = registry_path->MaximumLength;
		if (g_pRegistryPath->Buffer) {
			memcpy(g_pRegistryPath->Buffer, registry_path->Buffer, g_pRegistryPath->Length);
		}
	}

	g_pUnloadWorkItem = (PWORK_QUEUE_ITEM)ExAllocatePool(NonPagedPool, sizeof(WORK_QUEUE_ITEM));
}

VOID SelfUnloadExec() {
	if (g_pRegistryPath && g_pRegistryPath->Buffer && g_pUnloadWorkItem) {
		auto pfnZwUnloadDriver = utils::KGetProcAddress("ZwUnloadDriver");
		if (pfnZwUnloadDriver)
		{
			ExInitializeWorkItem(g_pUnloadWorkItem, (PWORKER_THREAD_ROUTINE)pfnZwUnloadDriver, g_pRegistryPath);
			DynamicFuncCallVoid(ExQueueWorkItem, g_pUnloadWorkItem, DelayedWorkQueue);
		}
	}
}

#pragma warning(pop)