#pragma once

#include <ntifs.h>
#include <ntddk.h>
#include <wdf.h>
#include <wdm.h>
#include <ntimage.h>
#include <ntstrsafe.h>
#include <Windef.h>
#include <intrin.h>

#include <cstddef>
#include <functional>
#include <atomic>
#include <string>
#include <mutex>
#include <memory>
#include <map>
#include <unordered_map>
#include <unordered_set>
#include <type_traits>
#include <chrono>
#include <coroutine>
#include <deque>
#include <algorithm>
#include <numeric>
#include <set>
#include <map>
#include <queue>

/******************************************/

#include "vmp.h"
#include "../../lib/Veil/Veil.h"



#include "base_type.hpp"
#include "kernel_api.hpp"
#include "defines.hpp"
#include "kthread.hpp"
#include "string.hpp"

#include "log.hpp"
#include "utils.hpp"
#include "pe.hpp"
#include "registry.hpp"

#include "ctx.hpp"
#include "workitem.hpp"
#include "file.hpp"
#include "thread.hpp"
#include "process.hpp"
#include "handle.hpp"
#include "notification.hpp"
//#include "rocksrv.hpp"
#include "main.hpp"
