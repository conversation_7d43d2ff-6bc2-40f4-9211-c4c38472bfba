#include "process.h"
#include "utils.h"
#include <string>
#include "public.h"

#pragma warning(push)
#pragma warning(disable : 4996)


PEPROCESS process::Lookup(HANDLE process_id)
{
	PEPROCESS eprocess = nullptr;
	DynamicFuncCallA(PsLookupProcessByProcessId, process_id, &eprocess);
	return eprocess;
}

void process::Unlookup(PEPROCESS eprocess)
{
	DynamicFuncCallA(ObfDereferenceObject, eprocess);
}

char* process::ShortName(PEPROCESS eprocess)
{
	return (char*)ImpCall(PsGetProcessImageFileName, eprocess);
}

bool process::GetFullPath(HANDLE processHandle, std::wstring& processPath)
{
	ULONG					needLen = 0;
	PUNICODE_STRING			imageFileName = NULL;
	NTSTATUS				status = STATUS_SUCCESS;

	processPath.clear();
	status = ZwQueryInformationProcess(processHandle, ProcessImageFileName, NULL, 0, &needLen);
	if ((status != STATUS_INFO_LENGTH_MISMATCH) || (needLen == 0))
	{
		return false;
	}

	needLen += sizeof(WCHAR);

	do
	{
		imageFileName = reinterpret_cast<PUNICODE_STRING>KRNL_ALLOC(needLen);
		if (imageFileName == nullptr)
		{
			break;
		}

		ZeroMemory(imageFileName, needLen);

		if (ZwQueryInformationProcess(processHandle, ProcessImageFileName,
			(PVOID)imageFileName, needLen, &needLen) != STATUS_SUCCESS)
		{
			break;
		}

		if (imageFileName->Length)
		{
			processPath = std::wstring(imageFileName->Buffer, imageFileName->Length);
		}

	} while (false);

	if (imageFileName)
	{
		KRNL_FREE(imageFileName);
	}

	return !processPath.empty();
}

bool process::IsCsrss(PEPROCESS eprocess)
{
	std::string process_name = ShortName(eprocess);
	return process_name == std::string("csrss.exe");
}

bool process::IsLsass(PEPROCESS eprocess)
{
	std::string process_name = ShortName(eprocess);
	return process_name == std::string("lsass.exe");
}

bool process::Is64(PEPROCESS eprocess)
{
	return ImpCall(PsGetProcessWow64Process, eprocess) == nullptr;
}

uint32_t process::ParentId(PEPROCESS eprocess)
{
	return HandleToULong(ImpCall(PsGetProcessInheritedFromUniqueProcessId, eprocess));
}

bool process::IsParent(PEPROCESS eprocess, PEPROCESS parent)
{
	return ParentId(eprocess) == HandleToULong(ImpCall(PsGetProcessId, parent));
}

PVOID process::HandleTable(PEPROCESS eprocess)
{
	PVOID result = nullptr;
	auto offset = 0;
	if (SharedUserData->NtMajorVersion == 6)
	{
		offset = 0x160;

		if (SharedUserData->NtMinorVersion == 1)
			offset = 0x200;
		if (SharedUserData->NtMinorVersion > 1)
			offset = 0x408;
	}
	if (SharedUserData->NtMajorVersion >= 10)
	{
		offset = 0x418;

		if (SharedUserData->NtBuildNumber >= 19041)
			offset = 0x570;

		if (SharedUserData->NtBuildNumber > 22631)
		{
			offset = *(int*)((unsigned char*)PsGetProcessWow64Process + 3) - 0x10;
		}
	}

	if (offset)
	{
		result = *(PVOID*)((unsigned char*)eprocess + offset);
	}

	return result;
}

PVOID process::ReferenceHandleTable(PEPROCESS eprocess)
{
	PVOID result = nullptr;
	if (NT_SUCCESS(ImpCall(PsAcquireProcessExitSynchronization, eprocess)))
	{
		result = HandleTable(eprocess);
		if (!result)
			ImpCall(PsReleaseProcessExitSynchronization, eprocess);
	}
	return result;
}

void process::DereferenceHandleTable(PEPROCESS eprocess)
{
	ImpCall(PsReleaseProcessExitSynchronization, eprocess);
}

bool process::EnumHandle(uint32_t process_id, PENUM_HANDLETABLE_ROUTINE func, PVOID context)
{
	bool result = false;
	auto eprocess = Lookup(ULongToHandle(process_id));
	if (eprocess)
	{
		auto handletable = ReferenceHandleTable(eprocess);
		if (handletable)
		{
			void* proxy_context[] = { context, func };
			auto proxy_procedure = [](void* table, void* entry, void* handle, void* context)->BOOLEAN {
				auto object_header = (*(intptr_t*)entry >> 16) & ~0x0F;
				auto handle_access = reinterpret_cast<PHANDLE_TABLE_ENTRY_WIN8>(entry)->u2.s.GrantedAccessBits;
				auto object = (void*)(object_header + 0x30);
				auto object_type = ImpCall(ObGetObjectType, object);

				auto mycontext = reinterpret_cast<void**>(context);
				auto original_context = mycontext[0];
				auto original_callback = reinterpret_cast<PENUM_HANDLETABLE_ROUTINE>(mycontext[1]);
				if (original_callback(handle, object, handle_access, original_context))
				{
					reinterpret_cast<PHANDLE_TABLE_ENTRY_WIN8>(entry)->u2.s.GrantedAccessBits = handle_access;
				}

				InterlockedExchangeAdd64(reinterpret_cast<LONG64*>(entry), 1);
				auto contention_event = (PEX_PUSH_LOCK)(reinterpret_cast<uintptr_t>(table) + 0x30);
				if (*(uintptr_t*)contention_event != 0)
				{
					ImpCall(ExfUnblockPushLock, contention_event, NULL);
				}
				return 0;
				};

			result = (bool)ImpCall(
				ExEnumHandleTable,
				handletable,
				proxy_procedure,
				proxy_context,
				nullptr
			);
			DereferenceHandleTable(eprocess);
		}
		Unlookup(eprocess);
	}
	return false;
}

bool process::OpenProcess(HANDLE ProcessId, HANDLE& ProcessHandle)
{
	OBJECT_ATTRIBUTES objAttr = { 0 };
	CLIENT_ID clientId = { 0 };
	HANDLE hProcess = NULL;

	InitializeObjectAttributes(&objAttr, NULL, OBJ_KERNEL_HANDLE, NULL, NULL);
	clientId.UniqueProcess = ProcessId;
	clientId.UniqueThread = NULL;

	if (NT_SUCCESS(ImpCall(ZwOpenProcess, &hProcess, PROCESS_ALL_ACCESS, &objAttr, &clientId)) == STATUS_SUCCESS)
	{
		return false;
	}

	ProcessHandle = hProcess;
	return true;
}

#pragma warning(pop)