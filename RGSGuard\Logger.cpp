#include "Logger.h"
#include "global.h"
#include "win_common.h"
#include <Config.h>

bool Logger::Init()
{
	return true;
}

void Logger::Release()
{
}

void Logger::Log(const char* function_name, const char* format, ...)
{
#ifdef _DEBUG
    if (!function_name || !format) return;

    static std::mutex logMtx;
    std::lock_guard<std::mutex> lock(logMtx);

    // ��ʽ���ɱ����
    va_list args;
    va_start(args, format);

    va_list args_copy;
    va_copy(args_copy, args);
    int needed = vsnprintf(nullptr, 0, format, args_copy);
    va_end(args_copy);

    if (needed <= 0) {
        va_end(args);
        return;
    }

    std::vector<char> buffer(needed + 1);
    vsnprintf(buffer.data(), buffer.size(), format, args);
    va_end(args);

    // ��ȡ�߳� ID����ƽ̨ע�⣺Windows ��ʹ�� GetCurrentThreadId��
#ifdef _WIN32
    DWORD threadId = GetCurrentThreadId();
#else
    auto threadId = std::this_thread::get_id();  // �� Windows ƽ̨
#endif

    // ����������־��Ϣ
    std::string message =
        "[RGSGuard] PID:" + std::to_string(GetCurrentProcessId()) +
#ifdef _WIN32
        " TID:" + std::to_string(threadId) +
#else
        " TID:" + std::to_string(std::hash<std::thread::id>{}(threadId)) +
#endif
        " " + function_name +
        " => " + buffer.data() + "\n";

    std::cout << message;
    OutputDebugStringA(message.c_str());
#endif
}
