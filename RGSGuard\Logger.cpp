#include "Logger.h"
#include "global.h"
#include "win_common.h"
#include <Config.h>

bool Logger::Init()
{
	return true;
}

void Logger::Release()
{
}

void Logger::Log(const char* function_name, const char* format, ...)
{
#ifdef _DEBUG
    if (!function_name || !format) return;

    std::lock_guard<std::mutex> lock(getLogMutex());

    va_list args;
    va_start(args, format);

    va_list args_copy;
    va_copy(args_copy, args);
    int needed = vsnprintf(nullptr, 0, format, args_copy);
    va_end(args_copy);

    if (needed <= 0) 
    {
        va_end(args);
        return;
    }

    std::vector<char> buffer(needed + 1);
    vsnprintf(buffer.data(), buffer.size(), format, args);
    va_end(args);

    std::string threadName = debug_helper::GetThreadName();
    if (threadName.empty()) threadName = VSA("<δ�����߳�>");
    else threadName = GetModuleDescription(threadName);

    std::string message =
        "[RGSGuard] PID:" + std::to_string(process::get_self_process_id()) +
        " TID:" + threadName +
        " " + function_name +
        " => " + buffer.data() + "\n";

    std::cout << message;
    OutputDebugStringA(message.c_str());
#endif
}
