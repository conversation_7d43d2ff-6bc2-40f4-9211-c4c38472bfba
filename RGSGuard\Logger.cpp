#include "Logger.h"
#include "global.h"
#include "win_common.h"
#include <Config.h>

bool Logger::Init()
{
	return true;
}

void Logger::Release()
{
}

void Logger::Log(const char* function_name, const char* format, ...)
{
#ifdef _DEBUG
    static std::mutex logMtx;
    std::lock_guard<std::mutex> lock(logMtx);

    char log_message[USN_PAGE_SIZE] = { 0 };
    va_list args;
    va_start(args, format);
    vsnprintf(log_message, sizeof(log_message), format, args);
    va_end(args);

    std::string message = std::string("[RGSGuard] ") + std::to_string(GetCurrentProcessId()) + " :" +
        std::string(function_name) + " => " + log_message + "\n";

    std::cout << message;
    OutputDebugStringA(message.c_str());
#endif
}
