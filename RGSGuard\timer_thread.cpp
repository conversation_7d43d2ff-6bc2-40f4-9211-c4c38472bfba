#include "timer_thread.h"
#include <Windows.h>

void TimerThread::Pause(std::string thread_key)
{
	std::lock_guard<std::mutex> lock(_thread_lock);

	auto it = _thread_obj_maps.find(thread_key);
	if (it != _thread_obj_maps.end() && it->second)
	{
		it->second->Pause();
	}
}

void TimerThread::Resume(std::string thread_key)
{
	std::lock_guard<std::mutex> lock(_thread_lock);

	auto it = _thread_obj_maps.find(thread_key);
	if (it != _thread_obj_maps.end() && it->second)
	{
		it->second->Resume();
	}
}

void TimerThread::KillThread(std::string thread_key)
{
	std::lock_guard<std::mutex> lock(_thread_lock);

	auto exit_it = _thread_exit_maps.find(thread_key);
	auto obj_it = _thread_obj_maps.find(thread_key);

	if (exit_it != _thread_exit_maps.end() && obj_it != _thread_obj_maps.end() && exit_it->second && obj_it->second)
	{
		exit_it->second->store(true);

		if ((*obj_it->second)->joinable()) 
		{
			(*obj_it->second)->join();
		}

		_thread_obj_maps.erase(obj_it);
		_thread_exit_maps.erase(exit_it);
	}
}

void TimerThread::KillAllThreads()
{
	std::lock_guard<std::mutex> lock(_thread_lock);

	for (auto& [key, exit_flag] : _thread_exit_maps) {
		if (exit_flag) {
			exit_flag->store(true);
			(*_thread_obj_maps[key])->join();
		}
	}

	for (auto& [key, tptr] : _thread_obj_maps) {
		if (tptr) {
			tptr->Terminate();
		}
	}

	_thread_obj_maps.clear();
	_thread_exit_maps.clear();
}
