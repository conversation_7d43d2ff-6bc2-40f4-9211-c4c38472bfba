#include "timed_heartbeat.h"
#include "rock_context.h"
#include "oxorany.h"

#define Rock_Heartbeat          77494


void TimedHeartbeat::Init()
{
}

void TimedHeartbeat::Process()
{
    VB;
    //g_ctx.deliver.PushToCenter(Rock_Heartbeat, oxorany("{\"Reserved\":\"\"}"));
    VE;
}

void TimedHeartbeat::Release()
{
}

int TimedHeartbeat::Interval()
{
    return 1000 * 10;
}

bool TimedHeartbeat::Promptly()
{
    return true;
}
