#pragma once
#include "pch.hpp"

namespace registry
{
	static bool reg_query_value(PUNICODE_STRING uskeyPath, PUNICODE_STRING usvalueName,
		PKEY_VALUE_PARTIAL_INFORMATION* pData)
	{
		NTSTATUS nStatus = STATUS_SUCCESS;
		HANDLE keyHandle = NULL;
		OBJECT_ATTRIBUTES objectAttributes = { 0 };
		PKEY_VALUE_PARTIAL_INFORMATION regDataPtr = NULL;
		ULONG valueSize = 0;

		InitializeObjectAttributes(&objectAttributes, uskeyPath, OBJ_CASE_INSENSITIVE, NULL, NULL);

		nStatus = ZwOpenKey(&keyHandle, KEY_ALL_ACCESS, &objectAttributes);
		if (!NT_SUCCESS(nStatus))
		{
			return false;
		}

		nStatus = ZwQueryValueKey(keyHandle,
			usvalueName,
			KeyValuePartialInformation,
			NULL,
			0,
			&valueSize);

		if (nStatus == STATUS_OBJECT_NAME_NOT_FOUND || valueSize == 0)
		{
			ZwClose(keyHandle);
			return false;
		}

		regDataPtr = (PKEY_VALUE_PARTIAL_INFORMATION)new unsigned char[valueSize];
		if (!regDataPtr)
			return false;

		nStatus = ZwQueryValueKey(keyHandle,
			usvalueName,
			KeyValuePartialInformation,
			regDataPtr,
			valueSize,
			&valueSize);

		if (!NT_SUCCESS(nStatus))
		{
			delete regDataPtr;
			ZwClose(keyHandle);
			return false;
		}

		*pData = regDataPtr;
		return true;
	}
}