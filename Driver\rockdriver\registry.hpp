#pragma once
#include "pch.hpp"

namespace registry
{
	typedef PUNICODE_STRING unistr_ptr;
	typedef OBJECT_ATTRIBUTES object_attributes;

	static ptr query_value(unistr_ptr key_path, unistr_ptr value_name)
	{
		ptr buf = nullptr;
		bool_t result = 0;
		ulong_t length = 0;
		handle_t keyhandle = nullptr;
		object_attributes attributes = { 0 };
		
		InitializeObjectAttributes(&attributes, key_path, OBJ_CASE_INSENSITIVE, NULL, NULL);
		if (NT_SUCCESS(ImpCall(ZwOpenKey, &keyhandle, KEY_ALL_ACCESS, &attributes)))
		{
			if ((ImpCall(ZwQueryValueKey, keyhandle, value_name, KeyValuePartialInformation, nullptr, 0, &length) == STATUS_BUFFER_TOO_SMALL) && (length > 0))
			{
#pragma warning(push)
#pragma warning(disable : 4996)
				buf = ImpCall(ExAllocatePoolWithTag, NonPagedPool, length, 'regq');
#pragma warning(pop)
				if (buf)
				{
					if (NT_SUCCESS(ImpCall(ZwQueryValueKey, keyhandle, value_name, KeyValuePartialInformation, buf, length, &length)))
					{
						result = 1;
					}
				}
			}
		}

		if (keyhandle)
		{
			ImpCall(ZwClose, keyhandle);
		}

		if (buf && result == 0)
		{
			ImpCall(ExFreePoolWithTag, buf, 'regq');
			buf = nullptr;
		}

		return buf;
	}

	static void release_value(ptr value)
	{
		if (value)
			ImpCall(ExFreePoolWithTag, value, 'regq');
	}

	static VOID delete_value(unistr_ptr usKeyName, unistr_ptr pValueName)
	{
		object_attributes attributes = { 0 };
		handle_t keyhandle = nullptr;

		InitializeObjectAttributes(&attributes, usKeyName,
			OBJ_CASE_INSENSITIVE, NULL, NULL);

		if (!NT_SUCCESS(ImpCall(ZwOpenKey, &keyhandle, KEY_ALL_ACCESS, &attributes)))
		{
			return;
		}

		ImpCall(ZwDeleteValueKey, keyhandle, pValueName);
		ImpCall(ZwClose, keyhandle);
		return;
	}
}