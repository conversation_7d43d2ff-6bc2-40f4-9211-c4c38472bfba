#include "rockguard.h"
#include "rock_context.h"
#include "notify_callback.h"
#include "process.h"
#include "rock_sig.h"

#define DEVICE_NAME L"\\Device\\RGDriver"
#define SYMBOL_NAME L"\\DosDevices\\RGDriver"

NTSTATUS RockGuard::Load(PDRIVER_OBJECT driver_object, PUNICODE_STRING registry_path)
{
	NTSTATUS status = 0;
	g_ctx.driver_object_ = driver_object;
	g_ctx.registry_path_ = std::wstring(registry_path->Buffer, registry_path->Length);

	do
	{
		UNICODE_STRING device_name = RTL_CONSTANT_STRING(DEVICE_NAME);
		UNICODE_STRING symbol_name = RTL_CONSTANT_STRING(SYMBOL_NAME);

		status = DynamicFuncCall(oxorany("ntoskrnl.exe"), IoCreateDevice, driver_object, 0, &device_name, FILE_DEVICE_UNKNOWN, 0, 0, &g_ctx.device_object_).value_or(STATUS_UNSUCCESSFUL);
		if (!NT_SUCCESS(status)) break;

		status = DynamicFuncCall(oxorany("ntoskrnl.exe"), IoCreateSymbolicLink, &symbol_name, &device_name).value_or(STATUS_UNSUCCESSFUL);
			
		status = NotifyCallback::Init();
		if (!NT_SUCCESS(status)) 
			break;

		driver_object->MajorFunction[IRP_MJ_CREATE] = DispatchCreate;
		driver_object->MajorFunction[IRP_MJ_CLOSE] = DispatchClose;
		driver_object->MajorFunction[IRP_MJ_WRITE] = DispatchWrite;
		driver_object->MajorFunction[IRP_MJ_READ] = DispatchRead;
		driver_object->MajorFunction[IRP_MJ_DEVICE_CONTROL] = DispatchIoControl;

	} while (false);

	if (NT_SUCCESS(status))
	{
		Init();
	}
	else
	{
		Release();
	}
	return status;
}

void RockGuard::UnLoad(PDRIVER_OBJECT driver_object)
{
	Release();
}

void RockGuard::Init()
{
	g_ctx.scheduler.Init();		//Stand
}

void RockGuard::Release()
{
	g_ctx.scheduler.Release();		//Stand

	if (NotifyCallback::initialized_)
		NotifyCallback::Release();

	UNICODE_STRING symbol_name = RTL_CONSTANT_STRING(SYMBOL_NAME);
	IoDeleteSymbolicLink(&symbol_name);

	if (g_ctx.device_object_)
		ImpCall(IoDeleteDevice, g_ctx.device_object_);
}

NTSTATUS RockGuard::DispatchCreate(PDEVICE_OBJECT p_device_object, PIRP p_irp) {
	p_irp->IoStatus.Status = STATUS_SUCCESS;
	p_irp->IoStatus.Information = 0;
	IoCompleteRequest(p_irp, IO_NO_INCREMENT);
	return STATUS_SUCCESS;
}

NTSTATUS RockGuard::DispatchRead(PDEVICE_OBJECT p_device_object, PIRP p_irp) {
	p_irp->IoStatus.Status = STATUS_SUCCESS;
	p_irp->IoStatus.Information = 0;
	IoCompleteRequest(p_irp, IO_NO_INCREMENT);
	return STATUS_SUCCESS;
}

NTSTATUS RockGuard::DispatchWrite(PDEVICE_OBJECT p_device_object, PIRP p_irp) {
	p_irp->IoStatus.Status = STATUS_SUCCESS;
	p_irp->IoStatus.Information = 0;
	IoCompleteRequest(p_irp, IO_NO_INCREMENT);
	return STATUS_SUCCESS;
}

NTSTATUS RockGuard::DispatchClose(PDEVICE_OBJECT p_device_object, PIRP p_irp) {
	p_irp->IoStatus.Status = STATUS_SUCCESS;
	p_irp->IoStatus.Information = 0;
	IoCompleteRequest(p_irp, IO_NO_INCREMENT);
	return STATUS_SUCCESS;
}

NTSTATUS RockGuard::DispatchIoControl(PDEVICE_OBJECT p_device_object, PIRP p_irp) 
{
	auto status = STATUS_UNSUCCESSFUL;

	PIO_STACK_LOCATION p_io_stack_loc = IoGetCurrentIrpStackLocation(p_irp);
	auto code = p_io_stack_loc->Parameters.DeviceIoControl.IoControlCode;
	auto len = p_io_stack_loc->Parameters.DeviceIoControl.InputBufferLength;
	auto msg = p_irp->AssociatedIrp.SystemBuffer;

	auto processId = HandleToUlong(PsGetCurrentProcessId());

	//if (IsRGSGuard(processId) == true)
	//{
	//	if (g_ctx.rock_driver.OnRequest(code, msg, len))
	//	{
	//		status = STATUS_SUCCESS;
	//	}
	//}

	if (g_ctx.rock_driver.OnRequest(code, msg, len))
	{
		status = STATUS_SUCCESS;
	}

	p_irp->IoStatus.Status = status;
	p_irp->IoStatus.Information = 0;
	IoCompleteRequest(p_irp, IO_NO_INCREMENT);
	return status;
}

bool RockGuard::IsRGSGuard(uint32_t process_id)
{
	bool result = false;

	HANDLE ProcessHandle = NULL;
	std::wstring processPath;
	std::vector<uint8_t> certificate;

	if (process_id == 0)
	{
		process_id = HandleToULong(PsGetCurrentProcessId());
	}

	do
	{
		if (process::OpenProcess(UlongToHandle(process_id), ProcessHandle) == false)
		{
			break;
		}

		if (process::GetFullPath(ProcessHandle, processPath) == false)
		{
			break;
		}

		if (utils::GetFileCertificateData(processPath.data(), certificate) == false)
		{
			break;
		}

		if ((certificate.size() == sizeof(hexData)) &&
			(memcmp(certificate.data(), hexData, certificate.size()) == 0))
		{
			result = false;
		}
	} while (false);

	if (ProcessHandle)
	{
		ZwClose(ProcessHandle);
	}

	return result;
}
