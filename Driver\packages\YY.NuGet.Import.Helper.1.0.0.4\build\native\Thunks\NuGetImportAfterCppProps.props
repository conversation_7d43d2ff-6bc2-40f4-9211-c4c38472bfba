﻿<?xml version="1.0" encoding="utf-8"?>


<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true'">
    <ForceImportAfterCppProps>$(YY_Import_Helper_Backup_ForceImportAfterCppProps)</ForceImportAfterCppProps>
    <YY_Import_Helper_Backup_ForceImportAfterCppProps></YY_Import_Helper_Backup_ForceImportAfterCppProps>
  </PropertyGroup>

  <ImportGroup Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true'">
    <!--先导入之前的 ForceImportAfterCppProps，这是从兼容性考虑。-->
    <Import Condition="Exists('$(ForceImportAfterCppProps)')" Project="$(ForceImportAfterCppProps)" />

    <Import Project="$(NuGetImportAfterCppProps)" Condition="'$(NuGetImportAfterCppProps)' != ''"/>
  </ImportGroup>
  
  <PropertyGroup>
    <NuGetImportAfterCppPropsComplete>true</NuGetImportAfterCppPropsComplete>
  </PropertyGroup>
</Project>