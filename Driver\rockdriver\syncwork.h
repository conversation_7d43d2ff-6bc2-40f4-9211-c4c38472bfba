#ifndef _SYNCWORK_
#define _SYNCWORK_
#include "public.h"
typedef void(*syncwork_callback)(void* context);

class SyncWork
{
public:
    SyncWork();
    ~SyncWork();
public:
    void Execute(syncwork_callback routine, PVOID context);
private:
    static void WrapperRoutine(PDEVICE_OBJECT device_object, PVOID context);
private:
    PVOID context_;
    KEVENT comple_;
    PIO_WORKITEM workitem_;

    syncwork_callback callback_;
};

#endif