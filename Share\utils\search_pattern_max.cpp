﻿#include <utils/search_pattern_max.h>
#include <unordered_map>
#include <emmintrin.h>
#include <intrin.h>

#ifdef _MSC_VER
#include <BaseTsd.h>
using ssize_t = SSIZE_T;
#endif

namespace search_pattern 
{
    std::string normalizePattern(const std::string& pat) {
        std::string res;
        for (char c : pat) {
            if (!std::isspace(static_cast<unsigned char>(c))) {
                res += static_cast<char>(std::tolower(static_cast<unsigned char>(c)));
            }
        }
        return res;
    }

    std::vector<bool> extractMask(const std::string& normalized) {
        std::vector<bool> mask;
        for (size_t i = 0; i + 1 < normalized.size(); i += 2) {
            mask.push_back(normalized[i] == '?' && normalized[i + 1] == '?');
        }
        return mask;
    }

    std::vector<uint8_t> patternToBytes(const std::string& normalized) {
        std::vector<uint8_t> bytes;
        auto hexToByte = [](char c) -> uint8_t {
            if (c >= '0' && c <= '9') return c - '0';
            if (c >= 'a' && c <= 'f') return c - 'a' + 10;
            return 0;
            };

        for (size_t i = 0; i + 1 < normalized.size(); i += 2) {
            if (normalized[i] == '?' && normalized[i + 1] == '?') {
                bytes.push_back(0); // 值不重要，有掩码跳过
            }
            else {
                uint8_t byte = (hexToByte(normalized[i]) << 4) | hexToByte(normalized[i + 1]);
                bytes.push_back(byte);
            }
        }
        return bytes;
    }
}

namespace search_pattern
{
    struct PatternSSE2Info {
        std::vector<bool> mask_bits;
        std::vector<uint8_t> pattern_bytes;
        std::vector<__m128i> pattern_chunks, mask_chunks;
        std::vector<int> expected_masks;
        std::vector<size_t> jump_table;
        size_t len = 0;
        size_t num_chunks = 0;
    };

    static std::unordered_map<std::string, PatternSSE2Info> g_sse2_cache;

    const PatternSSE2Info& getOrCompilePatternSSE2(const std::string& pattern) {
        std::string norm = normalizePattern(pattern);
        auto it = g_sse2_cache.find(norm);
        if (it != g_sse2_cache.end()) return it->second;

        PatternSSE2Info info;
        info.mask_bits = extractMask(norm);
        info.pattern_bytes = patternToBytes(norm);
        info.len = info.pattern_bytes.size();
        info.num_chunks = (info.len + 15) / 16;

        // 分块
        for (size_t c = 0; c < info.num_chunks; ++c) {
            alignas(16) uint8_t pat[16] = {}, msk[16] = {};
            for (size_t i = 0; i < 16; ++i) {
                size_t idx = c * 16 + i;
                if (idx >= info.len) break;
                pat[i] = info.pattern_bytes[idx];
                msk[i] = info.mask_bits[idx] ? 0x00 : 0xFF;
            }
            info.pattern_chunks.push_back(_mm_loadu_si128(reinterpret_cast<const __m128i*>(pat)));
            info.mask_chunks.push_back(_mm_loadu_si128(reinterpret_cast<const __m128i*>(msk)));
            size_t chunk_len = (c < info.num_chunks - 1) ? 16 : (info.len % 16 == 0 ? 16 : info.len % 16);
            info.expected_masks.push_back((1 << chunk_len) - 1);
        }

        // Sunday跳表
        info.jump_table.assign(256, info.len + 1);
        size_t last = info.len - 1, lastidx = last;
        while (lastidx > 0 && info.mask_bits[lastidx]) lastidx--;
        for (size_t i = 0; i < info.len; ++i) {
            if (!info.mask_bits[i])
                info.jump_table[info.pattern_bytes[i]] = info.len - i;
        }

        g_sse2_cache[norm] = std::move(info);
        return g_sse2_cache[norm];
    }

    struct PatternInfo {
        std::vector<bool>   mask;
        std::vector<uint8_t>bytes;
        std::vector<size_t> badChar;
        std::vector<size_t> goodSuffix;
    };

    PatternInfo compileBMPattern(const std::string& norm) {
        PatternInfo info;
        info.mask = extractMask(norm);
        info.bytes = patternToBytes(norm);
        const size_t plen = info.bytes.size();

        info.badChar.assign(256, plen);
        for (size_t i = 0; i < plen; ++i) {
            size_t shift = plen - 1 - i;
            if (info.mask[i]) {
                for (size_t b = 0; b < 256; ++b)
                    if (info.badChar[b] > shift) info.badChar[b] = shift;
            }
            else {
                uint8_t val = info.bytes[i];
                info.badChar[val] = shift;
            }
        }

        info.goodSuffix.assign(plen, plen);
        std::vector<size_t> suff(plen, 0);

        suff[plen - 1] = plen;
        for (ssize_t i = static_cast<ssize_t>(plen) - 2, g = static_cast<ssize_t>(plen) - 1, f = 0; i >= 0; --i) {
            if (i > g && suff[i + plen - 1 - f] < static_cast<size_t>(i - g)) {
                suff[i] = suff[i + plen - 1 - f];
            }
            else {
                if (i < g) g = i;
                f = static_cast<ssize_t>(i);
                while (g >= 0 && !info.mask[g] && !info.mask[g + plen - 1 - f] &&
                    info.bytes[g] == info.bytes[g + plen - 1 - f])
                    --g;
                suff[i] = static_cast<size_t>(f - g);
            }
        }

        std::fill(info.goodSuffix.begin(), info.goodSuffix.end(), plen);
        for (ssize_t i = static_cast<ssize_t>(plen) - 1; i >= 0; --i) {
            if (static_cast<size_t>(i) + 1 == suff[i]) {
                for (size_t j = 0; j < plen - 1 - static_cast<size_t>(i); ++j)
                    if (info.goodSuffix[j] == plen)
                        info.goodSuffix[j] = plen - 1 - static_cast<size_t>(i);
            }
        }
        for (size_t i = 0; i <= plen - 2; ++i)
            info.goodSuffix[plen - 1 - suff[i]] = plen - 1 - i;

        return info;
    }

    const PatternInfo& getOrCompileBM(const std::string& patternRaw) {
        static std::unordered_map<std::string, PatternInfo> cache;
        const std::string norm = normalizePattern(patternRaw);

        auto it = cache.find(norm);
        if (it != cache.end()) return it->second;

        PatternInfo info = compileBMPattern(norm);
        auto [iter, _] = cache.emplace(norm, std::move(info));
        return iter->second;
    }

    bool isSSE2Supported()
    {
        uint32_t edx;
        int cpuInfo[4];
        __cpuid(cpuInfo, 1);
        edx = cpuInfo[3];
        return (edx & (1 << 26)) != 0;
    }

    std::unordered_map<uintptr_t, uint64_t> findUInt64OffsetsAuto(const std::vector<uint8_t>& buffer, const std::unordered_set<uint64_t>& keys)
    {
        if (isSSE2Supported())
        {
            return findUInt64Offsets_SSE2(buffer, keys);
        }
        else
        {
            return findUInt64Offsets(buffer, keys);
        }
    }

    std::unordered_map<uintptr_t, uint32_t> findUInt32OffsetsAuto(const std::vector<uint8_t>& buffer, const std::unordered_set<uint32_t>& keys)
    {
        if (isSSE2Supported())
        {
            return findUInt32Offsets_SSE2(buffer, keys);
        }
        else
        {
            return findUInt32Offsets(buffer, keys);
        }
    }

    uintptr_t findFirstPatternOffsetAuto(const std::vector<uint8_t>& buffer, const std::string& pattern)
    {
        if (isSSE2Supported())
        {
            return findFirstPatternOffset_SSE2(buffer, pattern);
        }
        else
        {
            return findFirstPatternOffset(buffer, pattern);
        }
    }

    std::unordered_map<std::string, uintptr_t> findFirstPatternOffsetsAuto(const std::vector<uint8_t>& buffer, const std::unordered_set<std::string>& patterns)
    {
        if (isSSE2Supported())
        {
            return findFirstPatternOffsets_SSE2(buffer, patterns);
        }
        else
        {
            return findFirstPatternOffsets(buffer, patterns);
        };
    }

    std::unordered_map<uintptr_t, uint64_t> findUInt64Offsets_SSE2(const std::vector<uint8_t>& buffer,
        const std::unordered_set<uint64_t>& keys)
    {
        std::unordered_map<uintptr_t, uint64_t> result;
        size_t n = buffer.size();
        const uint8_t* buf = buffer.data();

        for (uint64_t key : keys)
        {
            __m128i key2 = _mm_set1_epi64x(key);

            size_t i = 0;
            for (; i + 16 <= n; i += 16)
            {
                __m128i block;
                std::memcpy(&block, buf + i, 16);
                __m128i cmp = _mm_cmpeq_epi64(block, key2);
                int mask = _mm_movemask_pd(_mm_castsi128_pd(cmp));

                if (mask & 1)
                {
                    result[i] = key;
                }

                if (mask & 2)
                {
                    result[i + 8] = key;
                }
            }

            for (; i + 8 <= n; i += 8)
            {
                uint64_t val;
                std::memcpy(&val, buf + i, 8);
                if (val == key)
                {
                    result[i] = key;
                }
            }
        }

        return result;
    }

    std::unordered_map<uintptr_t, uint32_t> findUInt32Offsets_SSE2(const std::vector<uint8_t>& buffer,
        const std::unordered_set<uint32_t>& keys)
    {
        std::unordered_map<uintptr_t, uint32_t> result;
        size_t n = buffer.size();
        const uint8_t* buf = buffer.data();

        for (uint32_t key : keys)
        {
            __m128i key4 = _mm_set1_epi32(key);

            size_t i = 0;
            for (; i + 16 <= n; i += 16)
            {
                __m128i block;
                std::memcpy(&block, buf + i, 16);
                __m128i cmp = _mm_cmpeq_epi32(block, key4);
                int mask = _mm_movemask_ps(_mm_castsi128_ps(cmp));

                if (mask & 1)
                {
                    result[i] = key;
                }
                if (mask & 2)
                {
                    result[i + 4] = key;
                }
                if (mask & 4)
                {
                    result[i + 8] = key;
                }
                if (mask & 8)
                {
                    result[i + 12] = key;
                }
            }

            for (; i + 4 <= n; i += 4)
            {
                uint32_t val;
                std::memcpy(&val, buf + i, 4);
                if (val == key)
                {
                    result[i] = key;
                }
            }
        }

        return result;
    }

    std::unordered_map<uintptr_t, uint64_t> findUInt64Offsets(const std::vector<uint8_t>& buffer, const std::unordered_set<uint64_t>& keys)
    {
        std::unordered_map<uintptr_t, uint64_t> matches;

        const size_t bufSize = buffer.size();
        const size_t addrSize = sizeof(uint64_t);

        if (keys.empty() || bufSize < addrSize)
        {
            return matches;
        }

        const uint8_t* data = buffer.data();

        for (size_t i = 0; i <= bufSize - addrSize; ++i)
        {
            uintptr_t value = 0;
            std::memcpy(&value, data + i, addrSize);

            if (keys.find(value) != keys.end())
            {
                matches[static_cast<uintptr_t>(i)] = value;
            }
        }

        return matches;
    }

    std::unordered_map<uintptr_t, uint32_t> findUInt32Offsets(const std::vector<uint8_t>& buffer, const std::unordered_set<uint32_t>& keys)
    {
        std::unordered_map<uintptr_t, uint32_t> matches;

        const size_t bufSize = buffer.size();
        const size_t addrSize = sizeof(uint32_t);

        if (keys.empty() || bufSize < addrSize)
        {
            return matches;
        }

        const uint8_t* data = buffer.data();

        for (size_t i = 0; i <= bufSize - addrSize; ++i)
        {
            uint32_t value = 0;
            std::memcpy(&value, data + i, addrSize);

            if (keys.find(value) != keys.end())
            {
                matches[static_cast<uintptr_t>(i)] = value;
            }
        }

        return matches;
    }

    uintptr_t findFirstPatternOffset(const std::vector<uint8_t>& buffer, const std::string& pattern)
    {
        const PatternInfo& info = getOrCompileBM(pattern);
        const auto& bytes = info.bytes;
        const auto& mask = info.mask;

        const size_t plen = bytes.size();
        const size_t blen = buffer.size();
        if (plen == 0 || blen < plen) return static_cast<uintptr_t>(-1);

        size_t i = 0;
        while (i <= blen - plen) {
            ssize_t j = static_cast<ssize_t>(plen) - 1;
            while (j >= 0 && (mask[j] || buffer[i + static_cast<size_t>(j)] == bytes[j]))
                --j;
            if (j < 0) return static_cast<uintptr_t>(i);

            uint8_t  badc = buffer[i + static_cast<size_t>(j)];
            size_t   bcShift = info.badChar[badc];
            size_t   gsShift = info.goodSuffix[static_cast<size_t>(j)];
            i += std::max<size_t>(1, std::max(bcShift, gsShift));
        }
        return static_cast<uintptr_t>(-1);
    }

    std::unordered_map<std::string, uintptr_t> findFirstPatternOffsets(const std::vector<uint8_t>& buffer, const std::unordered_set<std::string>& patterns)
    {
        std::unordered_map<std::string, uintptr_t> offsets;
        for (const auto& pattern : patterns) {
            uintptr_t pos = findFirstPatternOffset(buffer, pattern);
            if (pos != uintptr_t(-1)) {
                offsets[pattern] = pos;
            }
        }
        return offsets;
    }

    uintptr_t findFirstPatternOffset_SSE2(const std::vector<uint8_t>& buffer, const std::string& pattern)
    {
        const PatternSSE2Info& info = getOrCompilePatternSSE2(pattern);

        size_t len = info.len;
        size_t num_chunks = info.num_chunks;
        if (len == 0 || buffer.size() < len)
            return uintptr_t(-1);

        const uint8_t* data = buffer.data();
        size_t bufsize = buffer.size();

        size_t i = 0;
        while (i <= bufsize - len) {
            // SIMD掩码比对
            bool matched = true;
            for (size_t c = 0; c < num_chunks; ++c) {
                const uint8_t* chunk_data = data + i + c * 16;
                __m128i block = _mm_loadu_si128(reinterpret_cast<const __m128i*>(chunk_data));
                __m128i pat = info.pattern_chunks[c];
                __m128i mask = info.mask_chunks[c];
                __m128i block_masked = _mm_and_si128(block, mask);
                __m128i patt_masked = _mm_and_si128(pat, mask);
                __m128i cmp = _mm_cmpeq_epi8(block_masked, patt_masked);
                int result_mask = _mm_movemask_epi8(cmp);
                if ((result_mask & info.expected_masks[c]) != info.expected_masks[c]) {
                    matched = false;
                    break;
                }
            }
            if (matched) return static_cast<uintptr_t>(i);

            // Sunday跳跃
            if (i + len >= bufsize) break;
            uint8_t next = data[i + len];
            size_t skip = info.jump_table[next];
            i += skip;
        }

        return uintptr_t(-1);
    }

    std::unordered_map<std::string, uintptr_t> findFirstPatternOffsets_SSE2(const std::vector<uint8_t>& buffer, const std::unordered_set<std::string>& patterns)
    {
        std::unordered_map<std::string, uintptr_t> offsets;
        for (const auto& pattern : patterns) {
            uintptr_t pos = findFirstPatternOffset_SSE2(buffer, pattern);
            if (pos != uintptr_t(-1)) {
                offsets[pattern] = pos;
            }
        }
        return offsets;
    }
}