#include "syncwork.h"
#include "rock_context.h"

SyncWork::SyncWork() : workitem_(nullptr), callback_(nullptr), context_(nullptr), comple_()
{
}

SyncWork::~SyncWork()
{
	if (workitem_) ImpCall(IoFreeWorkItem, workitem_);
}

void SyncWork::Execute(syncwork_callback routine, PVOID context)
{
    if (g_ctx.device_object_)
    {
        ImpCall(KeInitializeEvent, &comple_, NotificationEvent, FALSE);
        workitem_ = ImpCall(IoAllocateWorkItem, g_ctx.device_object_);
        if (workitem_)
        {
            callback_ = routine;
            context_ = context;
            ImpCall(IoQueueWorkItem, workitem_, WrapperRoutine, DelayedWorkQueue, this);
            ImpCall(KeWaitForSingleObject, &comple_, Executive, KernelMode, FALSE, nullptr);
        }
    }
}

void SyncWork::WrapperRoutine(PDEVICE_OBJECT device_object, PVOID context)
{
    auto workitem = static_cast<SyncWork*>(context);
    workitem->callback_(workitem->context_);
    ImpCall(KeSetEvent, &workitem->comple_, 0, FALSE);
}