#pragma once

#define DriverDisplayName	"8D9C3FAB-4F0A-44E1-AEAF-FDA14D88EDA5"
#define DriverServiceName	"8D9C3FAB-4F0A-44E1-AEAF-FDA14D88EDA5"

#define RGDriverSymlink		L"\\\\.\\111111111"

#define RockGuard_XorKey    "DNASYsad4-sbaydwAD.,SBAYW5494847231!@#"

#define RoleClient              0x22000C80

#define RockGuard_AddProtectProcess         4846545
#define RockGuard_DeleteProtectProcess      9845574


//驱动消息结构
#pragma pack(push, 4)
typedef struct _DriverCommand
{
	unsigned int Function;
	union
	{
		struct
		{
			unsigned int    process_id;
			unsigned char   process_name[256];
		}Protcet;
	}Command;
}DriverCommand, * PDriverCommand;
#pragma pack()
