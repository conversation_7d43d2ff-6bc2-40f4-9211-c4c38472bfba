﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Driver Files">
      <UniqueIdentifier>{8E41214B-6785-4CFE-B992-037D68949A14}</UniqueIdentifier>
      <Extensions>inf;inv;inx;mof;mc;</Extensions>
    </Filter>
    <Filter Include="Header Files\utils">
      <UniqueIdentifier>{ec28c88f-257b-4074-a89c-4b13baa7f2a0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\core">
      <UniqueIdentifier>{06da7fa5-4a30-4259-8d09-c985187a4d56}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\core\context">
      <UniqueIdentifier>{5c664576-4ccd-4853-8a89-0a61d8ae1b09}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\core\contact">
      <UniqueIdentifier>{7b0a949d-10d0-496a-8530-7458eea520fe}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\core\method">
      <UniqueIdentifier>{b901ad81-e071-4745-9b17-062d4c6fab5a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\unused">
      <UniqueIdentifier>{046da8f1-80f4-44cd-8895-55209bac521b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\core\scheduler">
      <UniqueIdentifier>{d974cea0-7cc5-475e-bb5b-ec57e0c11fb2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\core\collect">
      <UniqueIdentifier>{22d2a82d-ca11-45d8-b894-4fb1186ae943}</UniqueIdentifier>
    </Filter>
    <Filter Include="oxorany">
      <UniqueIdentifier>{3029fbee-e1c8-4e60-8e7a-2f0e7319cec0}</UniqueIdentifier>
    </Filter>
    <Filter Include="xor_encrypt">
      <UniqueIdentifier>{1472317a-ecee-48c9-9bb3-58e9607add27}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rock_context.cpp">
      <Filter>Header Files\core\context</Filter>
    </ClCompile>
    <ClCompile Include="notify_callback.cpp">
      <Filter>Header Files\core\method</Filter>
    </ClCompile>
    <ClCompile Include="syncwork.cpp">
      <Filter>Header Files\core\method</Filter>
    </ClCompile>
    <ClCompile Include="rockguard.cpp">
      <Filter>Header Files\core</Filter>
    </ClCompile>
    <ClCompile Include="scheduler.cpp">
      <Filter>Header Files\core\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="timed_heartbeat.cpp">
      <Filter>Header Files\core\collect</Filter>
    </ClCompile>
    <ClCompile Include="lower_handleaccess.cpp">
      <Filter>Header Files\core\collect</Filter>
    </ClCompile>
    <ClCompile Include="process.cpp">
      <Filter>Header Files\utils</Filter>
    </ClCompile>
    <ClCompile Include="protect_rockac.cpp">
      <Filter>Header Files\core\collect</Filter>
    </ClCompile>
    <ClCompile Include="selfunload.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="oxorany.cpp">
      <Filter>oxorany</Filter>
    </ClCompile>
    <ClCompile Include="rock_driver.cpp">
      <Filter>Header Files\core\contact</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Lib\xor_encrypt\xor_encrypt.cpp">
      <Filter>xor_encrypt</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="vmp.h">
      <Filter>Header Files\utils</Filter>
    </ClInclude>
    <ClInclude Include="utils.h">
      <Filter>Header Files\utils</Filter>
    </ClInclude>
    <ClInclude Include="public.h">
      <Filter>Header Files\core</Filter>
    </ClInclude>
    <ClInclude Include="ctx.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="dynamic.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="kthread.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="notification.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="base_type.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="defines.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="rock_context.h">
      <Filter>Header Files\core\context</Filter>
    </ClInclude>
    <ClInclude Include="handle.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="notify_callback.h">
      <Filter>Header Files\core\method</Filter>
    </ClInclude>
    <ClInclude Include="main.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="pch.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="syncwork.h">
      <Filter>Header Files\core\method</Filter>
    </ClInclude>
    <ClInclude Include="rockguard.h">
      <Filter>Header Files\core</Filter>
    </ClInclude>
    <ClInclude Include="timer_thread.h">
      <Filter>Header Files\core</Filter>
    </ClInclude>
    <ClInclude Include="scheduler.h">
      <Filter>Header Files\core\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="timer_mod.h">
      <Filter>Header Files\core\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="timed_heartbeat.h">
      <Filter>Header Files\core\collect</Filter>
    </ClInclude>
    <ClInclude Include="lower_handleaccess.h">
      <Filter>Header Files\core\collect</Filter>
    </ClInclude>
    <ClInclude Include="process.h">
      <Filter>Header Files\utils</Filter>
    </ClInclude>
    <ClInclude Include="thread.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="string.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="registry.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="process.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="pe.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="file.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="log.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="workitem.hpp">
      <Filter>Header Files\unused</Filter>
    </ClInclude>
    <ClInclude Include="kernel_api.h">
      <Filter>Header Files\utils</Filter>
    </ClInclude>
    <ClInclude Include="custom_define.h">
      <Filter>Header Files\utils</Filter>
    </ClInclude>
    <ClInclude Include="protect_rockac.h">
      <Filter>Header Files\core\collect</Filter>
    </ClInclude>
    <ClInclude Include="selfunload.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="oxorany.h">
      <Filter>oxorany</Filter>
    </ClInclude>
    <ClInclude Include="oxorany_include.h">
      <Filter>oxorany</Filter>
    </ClInclude>
    <ClInclude Include="rock_driver.h">
      <Filter>Header Files\core\contact</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Lib\xor_encrypt\xor_encrypt.h">
      <Filter>xor_encrypt</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
</Project>