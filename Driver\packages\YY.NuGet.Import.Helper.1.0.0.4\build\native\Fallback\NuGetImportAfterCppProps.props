﻿<?xml version="1.0" encoding="utf-8"?>


<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Choose>
    <When Condition="'$(VisualStudioVersion)' == '' Or '$(VisualStudioVersion)'  &lt; '16.0'">
      <PropertyGroup Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true'">
        <UserRootDir>$(YY_Import_Helper_Backup_UserRootDir)</UserRootDir>
        <YY_Import_Helper_Backup_UserRootDir></YY_Import_Helper_Backup_UserRootDir>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true'">
        <ForceImportAfterCppProps>$(YY_Import_Helper_Backup_ForceImportAfterCppProps)</ForceImportAfterCppProps>
        <YY_Import_Helper_Backup_ForceImportAfterCppProps></YY_Import_Helper_Backup_ForceImportAfterCppProps>
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <ImportGroup Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true'">
    <Import Project="$(NuGetImportAfterCppProps)" Condition="'$(NuGetImportAfterCppProps)' != ''"/>
  </ImportGroup>
  
  <PropertyGroup>
    <NuGetImportAfterCppPropsComplete>true</NuGetImportAfterCppPropsComplete>
  </PropertyGroup>
</Project>