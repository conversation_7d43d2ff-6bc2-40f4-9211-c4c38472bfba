#include "protect_rockac.h"
#include "rock_context.h"
#include "process.h"
#include "selfunload.h"
#include "oxorany.h"

uint32_t ProtectProcessEmptyOverTime = 0;

void ProtectRockac::Init()
{
}

void ProtectRockac::Process()
{
	uint32_t rockacId = 0;
	if (g_ctx.GetProtectProcess(oxorany("RGSGuard64.exe"), rockacId))
	{
		auto rockacEprocess = process::Lookup(UlongToHandle(rockacId));
		if (rockacEprocess)
		{
			DynamicFuncCallA(PsResumeProcess, (PEPROCESS)rockacEprocess);
			process::Unlookup(rockacEprocess);
		}
	}

	if (g_ctx.IsProtectProcessEmpty())
	{
		ProtectProcessEmptyOverTime++;
	}

	if (ProtectProcessEmptyOverTime == 20 * 2)
	{
		UNICODE_STRING registry_path;
		RtlInitUnicodeString(&registry_path, g_ctx.registry_path_.c_str());
		SelfUnloadInit(&registry_path);
		SelfUnloadExec();
		ProtectProcessEmptyOverTime = 0;
	}
}

void ProtectRockac::Release()
{
}

int ProtectRockac::Interval()
{
	return 1000 * 3;
}

bool ProtectRockac::Promptly()
{
    return false;
}
