<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" InitialTargets="UCXXRT_PlatformPrepareForBuild">

  <!--
  作者：MiroKaku
  修改日期：06/08/2022

  ucxxrt 自动化加载配置，建议你将此文件单独复制到你的工程再使用，该文件能自动识别当前环境是否存在 ucxxrt，并且自动应用。

  使用方法：
    1. 在属性管理器选择所有配置，然后右键选择“添加现有属性表”。
    2. 务必保证所有依赖的静态库也均用 ucxxrt 重新编译。

  ucxxrt 默认搜索顺序
    1. 当前项目根目录，即 $(ProjectDir)ucxxrt
    2. 前解决方案根目录，即 $(SolutionDir)ucxxrt
    3. 当前项目父目录，即 $(ProjectDir)..\ucxxrt
    4. 当前解决方案父目录，即 $(SolutionDir)..\ucxxrt
    5. ucxxrt.props 所在目录，即 $(MSBuildThisFileDirectory)

  把 ucxxrt 放在其中一个位置即可，ucxxrt 就能被自动引用。

  如果你对默认搜索顺序不满，你可以修改此文件。你也可以直接指定$(UCXXRT_Root)宏更加任性的去加载 ucxxrt。
  -->

  <PropertyGroup>
    <!-- 当前项目根目录存在 ucxxrt？-->
    <UCXXRT_Root Condition=" ('$(UCXXRT_Root)'=='') And (Exists('$(ProjectDir)ucxxrt\ucxxrt.h')) ">$(ProjectDir)ucxxrt</UCXXRT_Root>

    <!-- 当前解决方案根目录存在 ucxxrt？-->
    <UCXXRT_Root Condition=" ('$(UCXXRT_Root)'=='') And ('$(SolutionDir)'!='') And (Exists('$(SolutionDir)ucxxrt\ucxxrt.h')) ">$(SolutionDir)ucxxrt</UCXXRT_Root>

    <!-- 当前项目父目录存在 ucxxrt？-->
    <UCXXRT_Root Condition=" ('$(UCXXRT_Root)'=='') And (Exists('$(ProjectDir)..\ucxxrt\ucxxrt.h')) ">$(ProjectDir)..\ucxxrt</UCXXRT_Root>

    <!-- 当前解决方案父目录存在 ucxxrt？-->
    <UCXXRT_Root Condition=" ('$(UCXXRT_Root)'=='') And ('$(SolutionDir)'!='') And (Exists('$(SolutionDir)..\ucxxrt\ucxxrt.h')) ">$(SolutionDir)..\ucxxrt</UCXXRT_Root>

    <!-- props 文件根目录存在 ucxxrt？-->
    <UCXXRT_Root Condition=" ('$(UCXXRT_Root)'=='') And (Exists('$(MSBuildThisFileDirectory)ucxxrt.h')) ">$(MSBuildThisFileDirectory)</UCXXRT_Root>
  </PropertyGroup>

  <PropertyGroup>
    <UCXXRT_KernelMode>false</UCXXRT_KernelMode>
    <UCXXRT_KernelMode Condition=" ('$(PlatformToolset.TrimEnd(`0123456789.`))' == 'WindowsKernelModeDriver') ">true</UCXXRT_KernelMode>
  </PropertyGroup>

  <PropertyGroup>
    <UCXXRT_Include>$(UCXXRT_Root)</UCXXRT_Include>
    <UCXXRT_Library>$(UCXXRT_Root)\lib\$(PlatformShortName)\$(Configuration)</UCXXRT_Library>
  </PropertyGroup>

  <PropertyGroup>
    <IncludePath>$(UCXXRT_Include);$(IncludePath)</IncludePath>
    <LibraryPath>$(UCXXRT_Library);$(LibraryPath)</LibraryPath>
  </PropertyGroup>

  <PropertyGroup Condition=" ('$(UCXXRT_KernelMode)'=='true') ">
    <!-- Enable Kernel C++ Support -->
    <DisableKernelFlag>true</DisableKernelFlag>
    <!-- Include STL -->
    <IncludePath>$(VC_IncludePath);$(IncludePath)</IncludePath>
  </PropertyGroup>

  <ItemDefinitionGroup Condition=" ('$(UCXXRT_KernelMode)'=='true') ">
    <ClCompile>
      <PreprocessorDefinitions>_NO_CRT_STDIO_INLINE;_HAS_EXCEPTIONS=1;_ITERATOR_DEBUG_LEVEL=0;__KERNEL_MODE=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <AdditionalOptions>/Zc:threadSafeInit- %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>libcntpr.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition=" ('$(UCXXRT_KernelMode)'=='true') ">
    <ClCompile>
      <PreprocessorDefinitions>_KERNEL_MODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup>
    <ClCompile>
      <!-- C++ Exception Mode, /EHa -->
      <ExceptionHandling>Async</ExceptionHandling>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <AdditionalDependencies>ucxxrt.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

  <Target Name="UCXXRT_PlatformPrepareForBuild">
    <Message Text="
  **************************************************
  *         Universal C++ RunTime (UCXXRT)         *
  **************************************************
  
  UCXXRT Path      : $(UCXXRT_Root)
  Platform         : $(PlatformShortName)
  Configuration    : $(Configuration)
  Tools Version    : $(VCToolsVersion)
  Platform Version : $(TargetPlatformVersion)
  Kernel Mode      : $(UCXXRT_KernelMode)
  " Importance="high" />
  </Target>
</Project>