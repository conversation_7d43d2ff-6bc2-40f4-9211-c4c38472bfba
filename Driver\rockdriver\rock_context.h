#ifndef _ROCK_CONTEXT_
#define _ROCK_CONTEXT_
#include <functional>
#include "public.h"
#include "syncwork.h"
#include "scheduler.h"
#include "rock_driver.h"
#include "timer_thread.h"

#include <map>
#include <mutex>
#include <string>

class RockContext
{
public:
	void AddProtectProcess(uint32_t process_id, std::string process_name);
	void RemoveProtectProcess(uint32_t process_id);
	bool IsProtectProcessEmpty();
	bool IsProtectProcess(uint32_t process_id);
	bool IsProtectProcess(PEPROCESS eprocess);
	bool IsWhiteProcess(PEPROCESS eprocess);
	bool GetProtectProcess(std::string process_name, uint32_t& process_Id);
public:
	PDEVICE_OBJECT device_object_;
	PDRIVER_OBJECT driver_object_;
	std::wstring registry_path_;
public:
	Scheduler scheduler;
	RockDriver rock_driver;
	TimerThread timer_thread;
public:

private:
	std::mutex protect_process_map_lock_;
	std::map<uint32_t, std::string>	protect_process_map_;
};

extern RockContext g_ctx;
#endif