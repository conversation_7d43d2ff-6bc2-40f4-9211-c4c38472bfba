﻿#include <utils/process_utils.h>
#include <utils/xstr_utils.h>
#include <utils/file_utils.h>
#include <utils/scan_vm_utils.h>
#include <utils/check_ptr.h>

#include <Veil/Veil.h>
#include <psapi.h>
#include <iphlpapi.h>
#include <shlobj_core.h>

#include <filesystem>
#include <regex>
#include <mutex>

namespace process
{
    void get_command_line(std::vector<std::string>& command)
    {
        try
        {
            int numArgs = 0;
            std::wstring commandLine = GetCommandLineW();
            if (commandLine.empty() == true)
            {
                return;
            }

            auto szArglist = CommandLineToArgvW(commandLine.c_str(), &numArgs);
            if (szArglist == nullptr || numArgs <= 0)
            {
                return;
            }

            for (int i = 0; i < numArgs; i++)
            {
                std::wstring argLine = szArglist[i];
                if (argLine.empty() == false)
                {
                    command.push_back(xstr::w2a(argLine));
                }
            }

            LocalFree(szArglist);
        }
        catch (const std::exception&)
        {

        }
    }

    void get_command_line(std::map<std::string, std::string>& command, std::function<void(std::string& command)> handleCommand)
    {
        try
        {
            std::vector<std::string> commandVector;
            get_command_line(commandVector);

            if (commandVector.empty())
            {
                return;
            }

            for (auto argLine : commandVector)
            {
                if (argLine.empty() == true)
                {
                    continue;
                }

                if (handleCommand)
                {
                    handleCommand(argLine);
                }

                std::regex pattern(R"((\w+)=([^\s]+))");
                std::smatch matches;

                std::string::const_iterator searchStart(argLine.cbegin());
                while (std::regex_search(searchStart, argLine.cend(), matches, pattern))
                {
                    command[matches[1]] = matches[2];
                    searchStart = matches.suffix().first;
                }
            }
        }
        catch (const std::exception&)
        {

        }
    }

    bool singleton_run(std::string name)
    {
        HANDLE hMutex = CreateMutexA(NULL, TRUE, name.c_str());
        if (!hMutex)
        {
            return false;
        }

        if (GetLastError() == ERROR_ALREADY_EXISTS) 
        {
            CloseHandle(hMutex);
            return false;
        }

        return true;
    }

    bool is_singleton_run(std::string name)
    {
        bool result = false;

        auto hMutex = OpenMutexA(MUTEX_ALL_ACCESS, FALSE, name.c_str());
        if (hMutex != NULL)
        {
            result = true;
            CloseHandle(hMutex);
        }

        return result;
    }

    bool is_running_as_admin()
    {
        BOOL isAdmin = FALSE;
        PSID adminGroup = NULL;
        SID_IDENTIFIER_AUTHORITY authority = SECURITY_NT_AUTHORITY;

        if (AllocateAndInitializeSid(&authority, 2, SECURITY_BUILTIN_DOMAIN_RID,
            DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, &adminGroup))
        {
            CheckTokenMembership(NULL, adminGroup, &isAdmin);
            FreeSid(adminGroup);
        }

        return (IsUserAnAdmin() == TRUE) && (isAdmin == TRUE);
    }

    bool is_process_alive(uint32_t processId)
    {
        if (processId == 0)
        {
            return false;
        }

        const auto& all_process = process::get_all_process();
        return all_process.find(processId) != all_process.cend();
    }

    bool enable_se_debug_privilege()
    {
        HANDLE hProcessToken = nullptr;
        LUID luidSeDebug;
        TOKEN_PRIVILEGES tokenPrivileges = { 0 };
        bool success = false;

        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hProcessToken))
            return false;

        do {
            if (!LookupPrivilegeValueW(nullptr, SE_DEBUG_NAME, &luidSeDebug))
                break;

            tokenPrivileges.PrivilegeCount = 1;
            tokenPrivileges.Privileges[0].Luid = luidSeDebug;
            tokenPrivileges.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

            if (!AdjustTokenPrivileges(hProcessToken, FALSE, &tokenPrivileges, sizeof(tokenPrivileges), nullptr, nullptr))
                break;

            success = (GetLastError() == ERROR_SUCCESS);
        } while (false);

        if (hProcessToken)
            CloseHandle(hProcessToken);

        return success;
    }

    uint32_t get_process_run_time_seconds(uint32_t processId)
    {
        if (processId == -1)
        {
            processId = GetCurrentProcessId();
        }

        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (hProcess == nullptr) {
            return 0;
        }

        FILETIME ftCreation, ftExit, ftKernel, ftUser;
        if (GetProcessTimes(hProcess, &ftCreation, &ftExit, &ftKernel, &ftUser)) {
            FILETIME ftNow;
            GetSystemTimeAsFileTime(&ftNow);

            ULARGE_INTEGER start, now;
            start.LowPart = ftCreation.dwLowDateTime;
            start.HighPart = ftCreation.dwHighDateTime;
            now.LowPart = ftNow.dwLowDateTime;
            now.HighPart = ftNow.dwHighDateTime;

            CloseHandle(hProcess);
            return static_cast<DWORD>((now.QuadPart - start.QuadPart) / 10000000);
        }

        CloseHandle(hProcess);
        return 0;
    }

    bool is32(std::uint32_t processId)
	{
        bool result = false;
        HANDLE hProcess = nullptr;

        do
        {

            hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
            if (hProcess == NULL)
            {
                break;
            }

            BOOL wow64Process = false;
            if (!IsWow64Process(hProcess, &wow64Process))
            {
                break;
            }

            result = wow64Process;

        } while (false);

        if (hProcess)
        {
            CloseHandle(hProcess);
        }

        return result;
	}

    bool is_console(std::string modulePath)
    {
        if (file::is_pe_file(modulePath) == false)
        {
            return false;
        }

        std::vector<uint8_t> peHdr;
        if (file::read_file(modulePath, 0x1000 * 2, peHdr) == false)
        {
            return false;
        }

        auto pDos = reinterpret_cast<const IMAGE_DOS_HEADER*>(peHdr.data());
        if (check_vector_ptr(peHdr, pDos, sizeof(*pDos)) == false)
        {
            return false;
        }

        if (pDos->e_magic != IMAGE_DOS_SIGNATURE)
        {
            return false;
        }

        auto pNt = reinterpret_cast<const IMAGE_NT_HEADERS*>(reinterpret_cast<const uint8_t*>(pDos) + pDos->e_lfanew);
        if (check_vector_ptr(peHdr, pNt, sizeof(*pNt)) == false)
        {
            return false;
        }

        if (pNt->Signature != IMAGE_NT_SIGNATURE)
        {
            return false;
        }

        WORD subsystem = 0;
        if (pNt->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR32_MAGIC)
        {
            auto pOpt32 = reinterpret_cast<const IMAGE_OPTIONAL_HEADER32*>(&pNt->OptionalHeader);
            subsystem = pOpt32->Subsystem;
        }
        else if (pNt->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR64_MAGIC)
        {
            auto pOpt64 = reinterpret_cast<const IMAGE_OPTIONAL_HEADER64*>(&pNt->OptionalHeader);
            subsystem = pOpt64->Subsystem;
        }
        else
        {
            return false;
        }

        return subsystem == IMAGE_SUBSYSTEM_WINDOWS_CUI;
    }

    bool is_console(std::uint32_t processId)
    {
        auto processPath = get_process_path(processId);
        if (processPath.empty())
        {
            return false;
        }
        return is_console(processPath);
    }

    void exit_process()
    {
        TerminateProcess(GetCurrentProcess(), 0);
    }

    void exit_process(std::uint32_t processId)
    {
        if (processId == 0)
        {
            return;
        }

        auto hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (hProcess) 
        {
            TerminateProcess(hProcess, 0);
            CloseHandle(hProcess);
        }
    }

    std::string get_self_path()
    {
        std::string selfPath;
        std::vector<char> buffer(MAX_PATH);

        while (buffer.size() < 0x1000)
        {
            DWORD copied = GetModuleFileNameA(NULL, buffer.data(), static_cast<DWORD>(buffer.size()));
            if (copied == 0)
            {
                break;
            }

            if (copied < buffer.size())
            {
                selfPath.assign(buffer.data(), copied);
                break;
            }

            buffer.resize(buffer.size() * 2);
        }

        return selfPath;
    }

    uint32_t get_self_process_id()
    {
        return GetCurrentProcessId();
    }

    std::string get_process_path(std::uint32_t processId)
    {
        std::string processPath;

        HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
        if (hProcess == NULL)
        {
            return processPath;
        }

        std::vector<char> buffer(MAX_PATH);

        while (buffer.size() < 0x1000)
        {
            DWORD size = static_cast<DWORD>(buffer.size());
            if (QueryFullProcessImageNameA(hProcess, 0, buffer.data(), &size))
            {
                processPath.assign(buffer.data(), size);
                break;
            }

            if (GetLastError() == ERROR_INSUFFICIENT_BUFFER)
            {
                buffer.resize(buffer.size() * 2);
            }
            else
            {
                break;
            }
        }

        CloseHandle(hProcess);
        return processPath;
    }

    bool create_process(std::string processPath, std::string commandLine, std::uint32_t* processId)
    {
        if (file::is_pe_file(processPath) == false)
        {
            return false;
        }

        STARTUPINFOA si{};
        si.cb = sizeof(si);
        PROCESS_INFORMATION pi{};

        // Windows API 要求命令行字符串可写，因此复制一份
        std::unique_ptr<char[]> cmdLineCopy;
        char* lpCmdLine = nullptr;
        if (!commandLine.empty())
        {
            cmdLineCopy = std::make_unique<char[]>(commandLine.size() + 1);
            std::memcpy(cmdLineCopy.get(), commandLine.c_str(), commandLine.size() + 1);
            lpCmdLine = cmdLineCopy.get();
        }

        BOOL success = CreateProcessA(
            processPath.c_str(),
            lpCmdLine,          // 可写命令行缓冲区
            nullptr,            // Process security
            nullptr,            // Thread security
            FALSE,              // Handle inheritance
            NORMAL_PRIORITY_CLASS | CREATE_NEW_CONSOLE,
            nullptr,            // Environment
            nullptr,            // Current directory
            &si,
            &pi);

        if (!success)
        {
            return false;
        }

        if (processId)
        {
            *processId = pi.dwProcessId;
        }

        // 释放资源
        CloseHandle(pi.hThread);
        CloseHandle(pi.hProcess);
        return true;
    }

    uint32_t find_processId_byname(std::string processName)
    {
        if (processName.empty() == true)
        {
            return 0;
        }

        const auto& all_process = process::get_all_process();

        for (const auto& processId : all_process)
        {
            const auto& processPath = process::get_process_path(processId);
            if (processPath.empty())
            {
                continue;
            }

            const auto& fileName = file::get_file_name(processPath);
            if (xstr::to_lower(fileName) == xstr::to_lower(processName))
            {
                return processId;
            }
        }

        return 0;
    }

    uint32_t find_processId_bypath(std::string processPath)
    {
        if (processPath.empty() == true)
        {
            return 0;
        }

        const auto& all_process = process::get_all_process();

        for (const auto& processId : all_process)
        {
            const auto& localProcessPath = process::get_process_path(processId);
            if (localProcessPath.empty())
            {
                continue;
            }

            if (xstr::to_lower(localProcessPath) == xstr::to_lower(processPath))
            {
                return processId;
            }
        }

        return 0;
    }

    bool isPrivateIp(const std::string& ip) 
    {
        uint32_t ipAddr = ntohl(inet_addr(ip.c_str()));
        if (ipAddr == INADDR_NONE) return false;

        // 检查10.0.0.0 - **************
        if ((ipAddr >= 0x0A000000) && (ipAddr <= 0x0AFFFFFF)) {
            return true;
        }

        // 检查********** - **************
        if ((ipAddr >= 0xAC100000) && (ipAddr <= 0xAC1FFFFF)) {
            return true;
        }

        // 检查*********** - ***************
        if ((ipAddr >= 0xC0A80000) && (ipAddr <= 0xC0A8FFFF)) {
            return true;
        }

        // 检查回环地址范围
        if ((ipAddr >= 0x7F000001) && (ipAddr <= 0x7FFFFFFF)) {
            return true;
        }

        return false;
    }

    std::mutex g_inet_ntoa_lock;
    std::string inet_ntoa_str(std::uint32_t dwAddr)
    {
        struct in_addr ipAddr;
        ipAddr.s_addr = dwAddr;

        g_inet_ntoa_lock.lock();
        auto result = std::string(inet_ntoa(ipAddr));
        g_inet_ntoa_lock.unlock();

        return result;
    }

    void get_process_tcp_connect(std::uint32_t processId, std::set<std::string>& remoteIpInfo)
    {
        PMIB_TCPTABLE2 ptcpTable = nullptr;
        DWORD tcpTableSize = 0;

        remoteIpInfo.clear();

        do
        {
            if (GetTcpTable2(ptcpTable, &tcpTableSize, TRUE) != ERROR_INSUFFICIENT_BUFFER)
            {
                break;
            }

            ptcpTable = static_cast<PMIB_TCPTABLE2>(malloc(static_cast<size_t>(tcpTableSize) + MAX_PATH));
            if (ptcpTable == nullptr)
            {
                break;
            }

            if (GetTcpTable2(ptcpTable, &tcpTableSize, TRUE) != NO_ERROR)
            {
                break;
            }

            for (DWORD i = 0; i < ptcpTable->dwNumEntries; ++i)
            {
                if (ptcpTable->table[i].dwState == MIB_TCP_STATE_ESTAB && ptcpTable->table[i].dwOwningPid == processId)
                {
                    auto processPath = get_process_path(ptcpTable->table[i].dwOwningPid);
                    auto remoteAddr = inet_ntoa_str(ptcpTable->table[i].dwRemoteAddr);
                    auto remotePort = ntohs(static_cast<u_short>(ptcpTable->table[i].dwRemotePort));
                    auto localAddr = inet_ntoa_str(ptcpTable->table[i].dwLocalAddr);
                    auto localPort = ntohs(static_cast<u_short>(ptcpTable->table[i].dwLocalPort));
                    if (!isPrivateIp(remoteAddr))
                    {
                        remoteIpInfo.insert(remoteAddr + ":" + std::to_string(remotePort));
                    }
                }
            }
        } while (false);

        if (ptcpTable)
        {
            free(ptcpTable);
        }

        return;
    }

    std::set<uint32_t> get_all_process()
    {
        std::set<uint32_t> processIds;

        ULONG bufferSize = 0;
        NTSTATUS status = NtQuerySystemInformation(SystemProcessInformation, nullptr, 0, &bufferSize);

        if (status != STATUS_INFO_LENGTH_MISMATCH) {
            return processIds;
        }

        int retryCount = 0;
        const int maxRetries = 10;
        std::vector<uint8_t> systemInformation;

        while (status == STATUS_INFO_LENGTH_MISMATCH && retryCount < maxRetries) {
            bufferSize += PAGE_SIZE_X86NT;
            systemInformation.resize(bufferSize);

            status = NtQuerySystemInformation(SystemProcessInformation, systemInformation.data(), bufferSize, &bufferSize);
            if (NT_SUCCESS(status)) {
                break;
            }

            retryCount++;
        }

        auto processInfo = reinterpret_cast<PSYSTEM_PROCESS_INFORMATION>(systemInformation.data());

        int maxIterations = 1500;
        int iterationCount = 0;

        while (processInfo != nullptr && iterationCount < maxIterations)
        {
            if (processInfo->UniqueProcessId)
            {
                processIds.insert(static_cast<uint32_t>(reinterpret_cast<uintptr_t>(processInfo->UniqueProcessId)));
            }

            if (processInfo->NextEntryOffset == 0) {
                break;
            }

            processInfo = reinterpret_cast<PSYSTEM_PROCESS_INFORMATION>(
                reinterpret_cast<BYTE*>(processInfo) + processInfo->NextEntryOffset
                );

            iterationCount++;
        }

        return processIds;
    }

    void enum_process_modules(std::uint32_t processId, std::vector<module_info>& modules)
    {
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
        if (hProcess == NULL)
        {
            return;
        }

        modules.clear();
        DWORD cbNeeded = 0;
        DWORD dwFilterFlag = is32(processId) ? LIST_MODULES_32BIT : LIST_MODULES_64BIT;

        if (EnumProcessModulesEx(hProcess, nullptr, 0, &cbNeeded, LIST_MODULES_ALL) && cbNeeded != 0)
        {
            // 多分配15个模块
            cbNeeded = (cbNeeded / sizeof(HMODULE) + 15) * sizeof(HMODULE);

            std::vector<HMODULE> hModules;
            hModules.resize(cbNeeded / sizeof(HMODULE));

            if (EnumProcessModulesEx(hProcess, hModules.data(), static_cast<DWORD>(hModules.size()) * sizeof(HMODULE), &cbNeeded, dwFilterFlag))
            {
                for (DWORD i = 0; i < (cbNeeded / sizeof(HMODULE)); i++)
                {
                    auto modulePath = get_image_mapped_file(processId, (std::uintptr_t)hModules[i]);
                    if (ConvertDosDevicePathToDrivePath(modulePath))
                    {
                        module_info module_data = {};

                        module_data.module_path = modulePath;
                        module_data.module_base = reinterpret_cast<std::uintptr_t>(hModules[i]);
                        module_data.module_size = get_module_size(processId, reinterpret_cast<std::uintptr_t>(hModules[i]));

                        modules.push_back(module_data);
                    }
                }
            }
        }

        CloseHandle(hProcess);
    }

    std::vector<module_info> get_process_all_modules(std::uint32_t processId)
    {
        std::vector<module_info> all_modules;
        enum_process_modules(processId, all_modules);
        return all_modules;
    }

    module_info get_process_module_info(std::uint32_t processId, std::string moduleName)
    {
        moduleName = xstr::to_lower(moduleName);
        auto all_modules = process::get_process_all_modules(processId);

        for (const auto& module_info : all_modules)
        {
            auto module_name = xstr::to_lower(file::get_file_name(module_info.module_path));
            if (module_name.empty() == true)
            {
                continue;
            }

            if (module_name == moduleName)
            {
                return module_info;
            }
        }

        return { std::string(),0, 0 };
    }

    uint32_t get_module_size(std::uint32_t processId, std::uintptr_t moduleBase)
    {
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
        if (!hProcess)
            return 0;

        MODULEINFO modInfo = {};
        bool success = GetModuleInformation(hProcess, reinterpret_cast<HMODULE>(moduleBase), &modInfo, sizeof(modInfo));
        CloseHandle(hProcess);

        return success ? static_cast<uint32_t>(modInfo.SizeOfImage) : 0;
    }

    void enum_process_memory(std::uint32_t processId, std::function<bool(MEM_INFO& memoryInfo)> memoryCallBack)
    {
        auto hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, false, processId);
        if (hProcess == nullptr)
        {
            return;
        }

        uint64_t curAddress = 0;
        MEMORY_BASIC_INFORMATION memoryInfo = { 0 };
        while (VirtualQueryEx(hProcess, (LPVOID)curAddress, &memoryInfo, sizeof(memoryInfo)))
        {
            MEM_INFO info = { 0 };
            info.BaseAddress = reinterpret_cast<uintptr_t>(memoryInfo.BaseAddress);
            info.AllocationBase = reinterpret_cast<uintptr_t>(memoryInfo.AllocationBase);
            info.AllocationProtect = static_cast<uintptr_t>(memoryInfo.AllocationProtect);
#if defined (_WIN64)
            info.PartitionId = static_cast<uintptr_t>(memoryInfo.PartitionId);
#endif
            info.RegionSize = static_cast<uintptr_t>(memoryInfo.RegionSize);
            info.State = static_cast<uintptr_t>(memoryInfo.State);
            info.Protect = static_cast<uintptr_t>(memoryInfo.Protect);
            info.Type = static_cast<uintptr_t>(memoryInfo.Type);

            if (memoryCallBack(info))
            {
                break;
            }
            curAddress += memoryInfo.RegionSize;
        }

        CloseHandle(hProcess);
    }

    std::vector<MEM_INFO> get_process_memory_layout(std::uint32_t processId)
    {
        std::vector<MEM_INFO> memoryRegions;

        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
        if (!hProcess)
        {
            return memoryRegions;
        }

        uint64_t curAddress = 0;
        MEMORY_BASIC_INFORMATION memoryInfo = { 0 };

        while (VirtualQueryEx(hProcess, reinterpret_cast<LPCVOID>(curAddress), &memoryInfo, sizeof(memoryInfo)))
        {
            MEM_INFO info = { 0 };
            info.BaseAddress = reinterpret_cast<uintptr_t>(memoryInfo.BaseAddress);
            info.AllocationBase = reinterpret_cast<uintptr_t>(memoryInfo.AllocationBase);
            info.AllocationProtect = static_cast<uint32_t>(memoryInfo.AllocationProtect);
#if defined (_WIN64)
            info.PartitionId = static_cast<uint16_t>(memoryInfo.PartitionId);
#endif
            info.RegionSize = static_cast<size_t>(memoryInfo.RegionSize);
            info.State = static_cast<uint32_t>(memoryInfo.State);
            info.Protect = static_cast<uint32_t>(memoryInfo.Protect);
            info.Type = static_cast<uint32_t>(memoryInfo.Type);

            memoryRegions.push_back(info);

            // 防止越界（尤其是在 x64 下）
            uint64_t next = curAddress + memoryInfo.RegionSize;
            if (next <= curAddress)
            {
                break;
            }

            curAddress = next;
        }

        CloseHandle(hProcess);
        return memoryRegions;
    }

    bool process_query_memory(std::uint32_t processId, std::uint64_t memaddr, MEM_INFO& memoryInfo)
    {
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
        if (!hProcess)
            return false;

        MEMORY_BASIC_INFORMATION mbi = { 0 };
        SIZE_T ret = VirtualQueryEx(hProcess, reinterpret_cast<LPCVOID>(memaddr), &mbi, sizeof(mbi));
        CloseHandle(hProcess);

        if (ret != sizeof(mbi))
            return false;

        memoryInfo.BaseAddress = reinterpret_cast<uintptr_t>(mbi.BaseAddress);
        memoryInfo.AllocationBase = reinterpret_cast<uintptr_t>(mbi.AllocationBase);
        memoryInfo.AllocationProtect = static_cast<uint32_t>(mbi.AllocationProtect);
#if defined(_WIN64)
        memoryInfo.PartitionId = static_cast<uint16_t>(mbi.PartitionId);
#endif
        memoryInfo.RegionSize = static_cast<size_t>(mbi.RegionSize);
        memoryInfo.State = static_cast<uint32_t>(mbi.State);
        memoryInfo.Protect = static_cast<uint32_t>(mbi.Protect);
        memoryInfo.Type = static_cast<uint32_t>(mbi.Type);

        return true;
    }

    void enum_process_thread(std::uint32_t processId, std::vector<std::pair<std::uint32_t, std::uintptr_t>>& threads)
    {
        ULONG infoSize = 0;
        PVOID systemInformation = nullptr;

        NTSTATUS status = NtQuerySystemInformation(SystemProcessInformation, nullptr, 0, &infoSize);
        if (status != STATUS_INFO_LENGTH_MISMATCH)
        {
            return;
        }

        systemInformation = malloc(static_cast<std::size_t>(infoSize) * 2);
        if (!systemInformation)
        {
            return;
        }

        status = NtQuerySystemInformation(SystemProcessInformation, systemInformation, infoSize * 2, &infoSize);
        if (!NT_SUCCESS(status))
        {
            free(systemInformation);
            return;
        }

        auto processInfo = static_cast<PSYSTEM_PROCESS_INFORMATION>(systemInformation);

        while (true)
        {
            if (processInfo->UniqueProcessId == LongToHandle(processId))
            {
                auto threadInfo = processInfo->Threads;
                auto threadCount = processInfo->NumberOfThreads;

                for (size_t i = 0; i < threadCount; i++)
                {
                    auto threadId = HandleToLong(threadInfo[i].ClientId.UniqueThread);
                    auto startAddress = reinterpret_cast<std::uintptr_t>(threadInfo[i].StartAddress);
                    threads.emplace_back(threadId, startAddress);
                }
                break;
            }

            if (processInfo->NextEntryOffset == 0)
            {
                break;
            }

            processInfo = reinterpret_cast<PSYSTEM_PROCESS_INFORMATION>(
                reinterpret_cast<ULONG_PTR>(processInfo) + processInfo->NextEntryOffset);
        }

        free(systemInformation);
    }

    void enum_all_topwindow(const std::function<bool(std::uint32_t processId,
        std::uint64_t hWnd, std::string windowText, std::string className)>& enumCallBack)
    {
        static auto EnumWindowsCallback = 
            [](HWND windowHwnd, LPARAM lParam)
        {
            auto enumCallBack = reinterpret_cast<std::function<bool(std::uint32_t processId,
                std::uint64_t hWnd, std::string windowText, std::string className)>*>(lParam);

            unsigned long processId = 0;
            char windowText[MAX_PATH * 2] = { 0 };
            char classNameText[MAX_PATH * 2] = { 0 };

            GetWindowThreadProcessId(windowHwnd, &processId);
            GetWindowTextA(windowHwnd, windowText, sizeof(windowText));
            GetClassNameA(windowHwnd, classNameText, sizeof(classNameText));

            std::string windowStr = windowText;
            std::string className = classNameText;

            return (BOOL)(*enumCallBack)((std::uint64_t)processId, (std::uint64_t)windowHwnd, windowStr, className);
        };
        EnumWindows(EnumWindowsCallback, (LPARAM)&enumCallBack);
    }

    void enum_all_child_window(std::uint64_t hWnd,
        std::function<bool(std::string className, std::string windowText)>& enumCallBack)
    {
        HWND hEnumWindow = (HWND)hWnd;
        auto EnumWindowsCallback =
            [](HWND windowHwnd, LPARAM lParam)
        {
            auto enumCallBack = reinterpret_cast<std::function<bool(std::string className, std::string windowText)>*>(lParam);
            unsigned long processId = 0;
            char windowText[MAX_PATH * 2] = { 0 };
            char className[MAX_PATH * 2] = { 0 };

            GetWindowThreadProcessId(windowHwnd, &processId);
            GetWindowTextA(windowHwnd, windowText, sizeof(windowText));
            GetClassNameA(windowHwnd, className, sizeof(className));

            std::string windowStr = windowText;
            std::string classStr = className;
            if ((*enumCallBack)(classStr, windowStr))
            {
                return FALSE;
            }
            else
            {
                return TRUE;
            }
        };
        EnumChildWindows(hEnumWindow, EnumWindowsCallback, (LPARAM)&enumCallBack);
    }

    void enum_process_top_window(std::uint32_t processId,
        std::function<bool(std::uint64_t hWnd, std::string windowText, std::string className)>& enumCallBack)
    {
        struct HWND_DATA {
            std::uint32_t processId = 0;
            std::function<bool(std::uint64_t hWnd, std::string windowText, std::string className)> enumCallBack = nullptr;
        } data;

        data.processId = processId;
        data.enumCallBack = enumCallBack;

        auto EnumWindowsCallback =
            [](HWND windowHwnd, LPARAM lParam)
        {
            HWND_DATA& data = *(HWND_DATA*)lParam;

            unsigned long processId = 0;
            GetWindowThreadProcessId(windowHwnd, &processId);
            if (processId != data.processId)
            {
                return TRUE;
            }

            char windowText[MAX_PATH * 2] = { 0 };
            char classNameText[MAX_PATH * 2] = { 0 };

            GetWindowTextA(windowHwnd, windowText, sizeof(windowText));
            GetClassNameA(windowHwnd, classNameText, sizeof(classNameText));

            std::string windowStr = windowText;
            std::string className = classNameText;

            return (BOOL)data.enumCallBack((std::uint64_t)windowHwnd, windowStr, className);
        };

        EnumWindows(EnumWindowsCallback, (LPARAM)&data);
    }

    std::vector<win_info> get_all_top_window()
    {
        std::vector<win_info> result;

        auto EnumWindowsCallback =
            [](HWND hwnd, LPARAM lParam)
            {
                if (lParam == NULL)
                {
                    return FALSE;
                }

                auto pResult = static_cast<std::vector<win_info>*>(reinterpret_cast<void*>(lParam));

                char windowText[MAX_PATH * 2] = { 0 };
                char classNameText[MAX_PATH * 2] = { 0 };

                GetWindowTextA(hwnd, windowText, sizeof(windowText));
                GetClassNameA(hwnd, classNameText, sizeof(classNameText));

                std::string windowStr = windowText;
                std::string className = classNameText;

                win_info info;
                info.hwnd = reinterpret_cast<uint64_t>(hwnd);
                info.window_name = windowText;
                info.class_name = classNameText;

                pResult->push_back(std::move(info));
                return TRUE;
            };

        EnumWindows(EnumWindowsCallback, (LPARAM)&result);
        return result;
    }

    uintptr_t get_process_main_window(std::uint32_t processId)
    {
        struct HWND_DATA {
            unsigned int processId;
            HWND hWnd;
        } data;

        data.processId = processId;
        data.hWnd = 0;

        static auto IsMainWindow = [](HWND handle) -> BOOL {
            return GetWindow(handle, GW_OWNER) == (HWND)0 && IsWindowVisible(handle);
        };

        static auto EnumWindowsCallback = [](HWND handle, LPARAM lParam)
        {
            HWND_DATA& data = *(HWND_DATA*)lParam;
            unsigned long process_id = 0;
            GetWindowThreadProcessId(handle, &process_id);
            if (data.processId != process_id || !IsMainWindow(handle)) {
                return TRUE;
            }
            data.hWnd = handle;
            return FALSE;
        };

        EnumWindows(EnumWindowsCallback, (LPARAM)&data);
        return reinterpret_cast<uintptr_t>(data.hWnd);
    }

    bool read_processmem(uint32_t processId, uint64_t targetAddress, uint64_t readBuffer, uint32_t len)
    {
        if (targetAddress == 0 || readBuffer == 0 || len == 0)
        {
            return false;
        }

        auto hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, false, processId);
        if (hProcess == NULL)
        {
            return false;
        }

        SIZE_T numberBytes = 0;
        auto result = ReadProcessMemory(hProcess, reinterpret_cast<void*>(targetAddress), 
            reinterpret_cast<void*>(readBuffer), static_cast<SIZE_T>(len), &numberBytes);
        CloseHandle(hProcess);

        return result && numberBytes == len;
    }

    bool write_processmem(std::uint32_t processId, std::uintptr_t targetAddress, std::uintptr_t writeBuffer, std::size_t len)
    {
        if (targetAddress == 0 || writeBuffer == 0 || len == 0)
        {
            return false;
        }

        auto hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_WRITE, false, processId);
        if (hProcess == NULL)
        {
            return false;
        }

        SIZE_T numberBytes = 0;
        auto result = WriteProcessMemory(hProcess, reinterpret_cast<void*>(targetAddress),
            reinterpret_cast<void*>(writeBuffer), static_cast<SIZE_T>(len), &numberBytes);
        CloseHandle(hProcess);

        return result && numberBytes == len;
    }

    bool read_own_memory(uint64_t targetAddress, uint64_t readBuffer, uint32_t len)
    {
        return read_processmem(GetCurrentProcessId(), targetAddress, readBuffer, len);
    }

    bool ConvertDosDevicePathToDrivePath(std::string& dosPath)
    {
        bool result = false;

        if (dosPath.empty() == true)
        {
            return false;
        }

        auto driveStrLen = GetLogicalDriveStringsA(0, NULL) * 2;
        if (driveStrLen <= 0)
        {
            return result;
        }

        std::vector<char> devicePathBuffer(driveStrLen + 1);

        if (GetLogicalDriveStringsA(static_cast<DWORD>(devicePathBuffer.size()), devicePathBuffer.data()))
        {
            char szDosDevicePath[MAX_PATH] = { 0 };
            char szDrive[3] = { 0 };
            char* p = devicePathBuffer.data();

            for (char* p = devicePathBuffer.data(); *p; p += strnlen(p, devicePathBuffer.size()) + 1)
            {
                szDrive[0] = *p;
                szDrive[1] = ':';
                szDrive[2] = '\0';

                if (QueryDosDeviceA(szDrive, szDosDevicePath, MAX_PATH))
                {
                    std::string devicePrefix = szDosDevicePath;
                    if (dosPath.find(devicePrefix) == 0)
                    {
                        dosPath = szDrive + dosPath.substr(devicePrefix.size());
                        result = true;
                        break;
                    }
                }
            }
        }

        return result;
    }

    bool is_access_allowed(std::uint32_t processId)
    {
        auto hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, false, processId);
        if (hProcess != 0)
        {
            CloseHandle(hProcess);
            return true;
        }
        else
        {
            return false;
        }
    }

    std::string get_image_mapped_file(std::uint32_t processId, std::uintptr_t memoryAddress)
    {
        std::string result;
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);

        if (hProcess == NULL)
        {
            return result;
        }

        MEMORY_BASIC_INFORMATION mbi = {};
        if (VirtualQueryEx(hProcess, reinterpret_cast<LPCVOID>(memoryAddress), &mbi, sizeof(mbi)) == 0 ||
            mbi.State != MEM_COMMIT || mbi.Type != MEM_IMAGE || mbi.AllocationBase == nullptr)
        {
            CloseHandle(hProcess);
            return result;
        }

        std::vector<char> buffer(MAX_PATH);
        while (buffer.size() < 0x1000)
        {
            memset(buffer.data(), 0, buffer.size());

            DWORD len = GetMappedFileNameA(hProcess, reinterpret_cast<void*>(memoryAddress), buffer.data(), static_cast<DWORD>(buffer.size()));
            if (len == 0)
            {
                break;
            }

            if (len < buffer.size())
            {
                result = std::string(buffer.data());
                break;
            }

            buffer.resize(buffer.size() * 2);
        }

        CloseHandle(hProcess);
        return result;
    }
}
