#pragma once
#include "pch.hpp"

namespace thread
{
	typedef PKAPC_STATE pkapc_state;


	pkapc_state apc_state(ptr thread)
	{
		u16 offset = 0;

		if (SharedUserData->NtMajorVersion > 6)
			offset = 0x98;//Win10 or new
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion > 1)
			offset = 0x98;//Win8
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion == 1)
			offset = 0x50;//Win7
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion == 0)
			offset = 0x50;//Vista
		if (offset)
			return *(pkapc_state*)((uintptr_t)thread + offset);
		else
			return (pkapc_state)(-1);
	}

	pkapc_state saved_apc_state(ptr thread)
	{
		u16 offset = 0;

		if (SharedUserData->NtMajorVersion > 6)
			offset = 0x258;//Win10 or new
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion > 1)
			offset = 0x258;//Win8
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion == 1)
			offset = 0x240;//Win7
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion == 0)
			offset = 0x240;//Vista
		if (offset)
			return *(pkapc_state*)((uintptr_t)thread + offset);
		else
			return (pkapc_state)(-1);
	}

	uint32_t syscall_number()
	{
		u16 offset = 0;

		if (SharedUserData->NtMajorVersion > 6)
			offset = 0x80;//Win10 or new
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion > 1)
			offset = 0x80;//Win8
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion == 1)
			offset = 0x1F8;//Win7
		else if (SharedUserData->NtMajorVersion == 6 && SharedUserData->NtMinorVersion == 0)
			offset = 0x1E8;//Vista

		if (offset)
			return *(uint32_t*)((uintptr_t)PsGetCurrentThread() + offset);
		else
			return (uint32_t)(-1);
	}

	static bool open_thread(handle_t processId, handle_t threadId, handle_t& hThread)
	{
		OBJECT_ATTRIBUTES objAttr = { 0 };
		CLIENT_ID clientId = { 0 };
		handle_t h_thread = nullptr;

		InitializeObjectAttributes(&objAttr, NULL, OBJ_KERNEL_HANDLE, NULL, NULL);
		clientId.UniqueProcess = processId;
		clientId.UniqueThread = threadId;

		if (!NT_SUCCESS(ImpCall(ZwOpenThread, &h_thread, THREAD_ALL_ACCESS, &objAttr, &clientId)))
		{
			return false;
		}

		hThread = h_thread;
		return true;
	}
}