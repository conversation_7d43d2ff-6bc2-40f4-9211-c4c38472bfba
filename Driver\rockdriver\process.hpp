#pragma once
#include <ntifs.h>

namespace process
{
	static ptr lookup_process(handle_t process_id)
	{
		ptr process = nullptr;
		ImpCall(PsLookupProcessByProcessId, process_id, reinterpret_cast<PEPROCESS*>(&process));
		return process;
	}

	static void unlookup_process(ptr process)
	{
		ImpCall(ObDereferenceObject, process);
	}

	static char* shortname(ptr process)
	{
		return (char*)ImpCall(PsGetProcessImageFileName, reinterpret_cast<PEPROCESS>(process));
	}

	static std::wstring fullpath(ptr process)
	{
		PFILE_OBJECT file_object = nullptr;
		if (NT_SUCCESS(ImpCall(PsReferenceProcessFilePointer, reinterpret_cast<PEPROCESS>(process), &file_object)))
		{
			auto result = file::query_dos_name(file_object);
			ImpCall(ObDereferenceObject, file_object);
			return result;
		}
		return L"";
	}

	static bool is64(ptr process)
	{
		return ImpCall(PsGetProcessWow64Process, reinterpret_cast<PEPROCESS>(process)) == nullptr;
	}

	static ptr peb32(ptr process)
	{
		return ImpCall(PsGetProcessWow64Process, reinterpret_cast<PEPROCESS>(process));
	}

	static ptr peb64(ptr process)
	{
		return ImpCall(PsGetProcessPeb, reinterpret_cast<PEPROCESS>(process));
	}

	static ptr imagebase(ptr process)
	{
		ptr result = nullptr;
		result = ImpCall(PsGetProcessSectionBaseAddress, reinterpret_cast<PEPROCESS>(process));
		return result;
	}

	static bool is_csrss(ptr process)
	{
		std::string processName = shortname(process);
		return processName == std::string("csrss.exe");
	}

	static bool is_lsass(ptr process)
	{
		std::string processName = shortname(process);
		return processName == std::string("lsass.exe");
	}

	static uint32_t parent_id(ptr process)
	{
		return HandleToULong(ImpCall(PsGetProcessInheritedFromUniqueProcessId, reinterpret_cast<PEPROCESS>(process)));
	}

	static bool is_parent(ptr process, ptr target)
	{
		return parent_id(process) == HandleToULong(ImpCall(PsGetProcessId, reinterpret_cast<PEPROCESS>(target)));
	}

	static ptr handletable(ptr process)
	{
		ptr result = nullptr;

		auto offset = 0;
		if (SharedUserData->NtMajorVersion == 6)
		{
			offset = 0x160;

			if (SharedUserData->NtMinorVersion == 1)
				offset = 0x200;
			if (SharedUserData->NtMinorVersion > 1)
				offset = 0x408;
		}
		if (SharedUserData->NtMajorVersion >= 10)
		{
			offset = 0x418;

			if (SharedUserData->NtBuildNumber >= 19041)
				offset = 0x570;

			if (SharedUserData->NtBuildNumber > 22631)
			{
				offset = *(int*)((u8ptr)PsGetProcessWow64Process + 3) - 0x10;
			}
		}

		if (offset)
		{
			result = *(ptr*)((u8ptr)process + offset);
		}

		return result;
	}

	static ptr reference_handletable(ptr process)
	{
		ptr result = nullptr;
		if (NT_SUCCESS(ImpCall(PsAcquireProcessExitSynchronization, reinterpret_cast<PEPROCESS>(process))))
		{
			result = handletable(process);
			if (!result)
				ImpCall(PsReleaseProcessExitSynchronization, reinterpret_cast<PEPROCESS>(process));
		}
		return result;
	}

	static void dereference_handletable(ptr process)
	{
		ImpCall(PsReleaseProcessExitSynchronization, reinterpret_cast<PEPROCESS>(process));
	}

	static void end_process(ptr process)
	{
		handle_t handle = nullptr;
		if (NT_SUCCESS(ImpCall(ObOpenObjectByPointer, reinterpret_cast<PEPROCESS>(process), OBJ_KERNEL_HANDLE, NULL, PROCESS_TERMINATE, *PsProcessType, KernelMode, &handle)))
		{
			ImpCall(ZwTerminateProcess, handle, 0);
			ImpCall(ZwClose, handle);
		}
	}

	static bool open_process(HANDLE ProcessId, HANDLE& ProcessHandle)
	{
		OBJECT_ATTRIBUTES objAttr = { 0 };
		CLIENT_ID clientId = { 0 };
		HANDLE hProcess = NULL;

		InitializeObjectAttributes(&objAttr, NULL, OBJ_KERNEL_HANDLE, NULL, NULL);
		clientId.UniqueProcess = ProcessId;
		clientId.UniqueThread = NULL;

		if (!NT_SUCCESS(ImpCall(ZwOpenProcess,&hProcess, PROCESS_ALL_ACCESS, &objAttr, &clientId)))
		{
			return false;
		}

		ProcessHandle = hProcess;
		return true;
	}
}