<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true'">
    <UserRootDir>$(YY_Import_Helper_Backup_UserRootDir)</UserRootDir>
    <YY_Import_Helper_Backup_UserRootDir></YY_Import_Helper_Backup_UserRootDir>
  </PropertyGroup>


  <ImportGroup Condition="'$(NuGetImportAfterCppPropsComplete)' != 'true'">
    <!--加载用户配置-->
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')"/>
    <Import Project="$(NuGetImportAfterCppProps)" Condition="'$(NuGetImportAfterCppProps)' != ''"/>
  </ImportGroup>

  <PropertyGroup>
    <NuGetImportAfterCppPropsComplete>true</NuGetImportAfterCppPropsComplete>
  </PropertyGroup>
</Project>
