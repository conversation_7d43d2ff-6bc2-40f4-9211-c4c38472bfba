#include "rock_context.h"
#include "rock_driver.h"
#include <xor_encrypt/xor_encrypt.h>

bool RockDriver::OnRequest(ULONG code, PVOID pmsg, ULONG len)
{
	std::string driverCommand = std::string(static_cast<char*>(pmsg), len);
	driverCommand = xor_decrypt(driverCommand, oxorany(RockGuard_XorKey));

	auto body = reinterpret_cast<PDriverCommand>(driverCommand.data());
	if (code == oxorany(RoleClient) && len == sizeof(DriverCommand))
	{
		auto res = true;
		if (body->Function == oxorany(RockGuard_AddProtectProcess))
		{
			g_ctx.AddProtectProcess(body->Command.Protcet.process_id, std::string((char*)body->Command.Protcet.process_name));
		}
		else if (body->Function == oxorany(RockGuard_DeleteProtectProcess))
		{
			g_ctx.RemoveProtectProcess(body->Command.Protcet.process_id);
		}
		return res;
	}

	return false;
}