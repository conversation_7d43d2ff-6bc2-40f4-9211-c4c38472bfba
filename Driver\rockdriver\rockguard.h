#ifndef _ROCKGUARD_
#define _ROCKGUARD_
#include "public.h"
class RockGuard
{
public:
	static NTSTATUS Load(PDRIVER_OBJECT driver_object, PUNICODE_STRING registry_path);
	static void UnLoad(PDRIVER_OBJECT driver_object);

	static NTSTATUS DispatchCreate(PDEVICE_OBJECT p_device_object, PIRP p_irp);
	static NTSTATUS DispatchRead(PDEVICE_OBJECT p_device_object, PIRP p_irp);
	static NTSTATUS DispatchWrite(PDEVICE_OBJECT p_device_object, PIRP p_irp);
	static NTSTATUS DispatchClose(PDEVICE_OBJECT p_device_object, PIRP p_irp);
	static NTSTATUS DispatchIoControl(PDEVICE_OBJECT p_device_object, PIRP p_irp);

	static bool IsRGSGuard(uint32_t process_id);

private:
	static void Init();
	static void Release();
};
#endif
